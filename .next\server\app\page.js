/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Ci18n-provider.tsx%22%2C%22ids%22%3A%5B%22I18nProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Csession-provider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Ci18n-provider.tsx%22%2C%22ids%22%3A%5B%22I18nProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Csession-provider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./providers/i18n-provider.tsx */ \"(ssr)/./providers/i18n-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./providers/session-provider.tsx */ \"(ssr)/./providers/session-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Ci18n-provider.tsx%22%2C%22ids%22%3A%5B%22I18nProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Csession-provider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBQ21DO0FBRzFELFNBQVNDLGNBQWMsRUFBRUUsUUFBUSxFQUFFLEdBQUdDLE9BQTJCO0lBQ3RFLHFCQUFPLDhEQUFDRixzREFBa0JBO1FBQUUsR0FBR0UsS0FBSztrQkFBR0Q7Ozs7OztBQUN6QyIsInNvdXJjZXMiOlsid2VicGFjazovL2FwcC8uL2NvbXBvbmVudHMvdGhlbWUtcHJvdmlkZXIudHN4PzkyODkiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgYXMgTmV4dFRoZW1lc1Byb3ZpZGVyIH0gZnJvbSBcIm5leHQtdGhlbWVzXCJcbmltcG9ydCB7IHR5cGUgVGhlbWVQcm92aWRlclByb3BzIH0gZnJvbSBcIm5leHQtdGhlbWVzL2Rpc3QvdHlwZXNcIlxuXG5leHBvcnQgZnVuY3Rpb24gVGhlbWVQcm92aWRlcih7IGNoaWxkcmVuLCAuLi5wcm9wcyB9OiBUaGVtZVByb3ZpZGVyUHJvcHMpIHtcbiAgcmV0dXJuIDxOZXh0VGhlbWVzUHJvdmlkZXIgey4uLnByb3BzfT57Y2hpbGRyZW59PC9OZXh0VGhlbWVzUHJvdmlkZXI+XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJUaGVtZVByb3ZpZGVyIiwiTmV4dFRoZW1lc1Byb3ZpZGVyIiwiY2hpbGRyZW4iLCJwcm9wcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/i18n.ts":
/*!*********************!*\
  !*** ./lib/i18n.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! i18next */ \"(ssr)/./node_modules/i18next/dist/esm/i18next.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var i18next_browser_languagedetector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! i18next-browser-languagedetector */ \"(ssr)/./node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js\");\n\n\n\nconst resources = {\n    ar: {\n        translation: {\n            // Navigation\n            \"dashboard\": \"لوحة التحكم\",\n            \"profile\": \"الملف الشخصي\",\n            \"requests\": \"الطلبات\",\n            \"tasks\": \"المهام\",\n            \"reports\": \"التقارير\",\n            \"settings\": \"الإعدادات\",\n            \"logout\": \"تسجيل الخروج\",\n            // Authentication\n            \"login\": \"تسجيل الدخول\",\n            \"register\": \"إنشاء حساب\",\n            \"email\": \"البريد الإلكتروني\",\n            \"password\": \"كلمة المرور\",\n            \"full_name\": \"الاسم الكامل\",\n            \"national_id\": \"رقم الهوية الوطنية\",\n            \"phone_number\": \"رقم الهاتف\",\n            \"login_tawtheeq\": \"تسجيل الدخول عبر توثيق\",\n            \"login_success\": \"تم تسجيل الدخول بنجاح\",\n            \"login_error\": \"خطأ في البيانات المدخلة\",\n            // Dashboard Enhancements\n            \"welcome\": \"مرحباً\",\n            \"overview_status\": \"نظرة عامة على حالة\",\n            \"system_overview\": \"النظام\",\n            \"your_requests\": \"طلباتك\",\n            \"total_beneficiaries\": \"إجمالي المستفيدين\",\n            \"total_zakat_distributed\": \"إجمالي الزكاة الموزعة\",\n            \"pending_applications\": \"الطلبات المعلقة\",\n            \"active_distributions\": \"التوزيعات النشطة\",\n            \"quick_actions\": \"الإجراءات السريعة\",\n            \"register_new_beneficiary\": \"تسجيل مستفيد جديد\",\n            \"search_beneficiaries\": \"البحث عن المستفيدين\",\n            \"generate_report\": \"إنشاء تقرير\",\n            \"manage_distributions\": \"إدارة التوزيعات\",\n            \"recent_activity\": \"النشاط الأخير\",\n            \"distribution_overview\": \"نظرة عامة على التوزيع\",\n            \"monthly_distributions\": \"التوزيعات الشهرية\",\n            \"beneficiary_categories\": \"فئات المستفيدين\",\n            \"view_all\": \"عرض الكل\",\n            \"this_month\": \"هذا الشهر\",\n            \"last_month\": \"الشهر الماضي\",\n            \"last_week\": \"الأسبوع الماضي\",\n            \"this_week\": \"هذا الأسبوع\",\n            \"increase\": \"زيادة\",\n            \"decrease\": \"انخفاض\",\n            \"from\": \"من\",\n            \"sar\": \"ريال سعودي\",\n            \"beneficiaries\": \"مستفيدين\",\n            \"applications\": \"طلبات\",\n            \"distributions\": \"توزيعات\",\n            \"completed\": \"مكتمل\",\n            \"pending\": \"معلق\",\n            \"warning\": \"تحذير\",\n            // Profile\n            \"personal_profile\": \"الملف الشخصي\",\n            \"basic_info\": \"المعلومات الأساسية\",\n            \"family_info\": \"بيانات الأسرة\",\n            \"employment_info\": \"بيانات العمل\",\n            \"financial_info\": \"المعلومات المالية\",\n            \"marital_status\": \"الحالة الاجتماعية\",\n            \"single\": \"أعزب\",\n            \"married\": \"متزوج\",\n            \"divorced\": \"مطلق\",\n            \"widowed\": \"أرمل\",\n            // Assistance Requests\n            \"assistance_request\": \"طلب المساعدة\",\n            \"new_request\": \"طلب جديد\",\n            \"request_type\": \"نوع المساعدة\",\n            \"requested_amount\": \"المبلغ المطلوب\",\n            \"approved_amount\": \"المبلغ المعتمد\",\n            \"description\": \"الوصف\",\n            \"attach_documents\": \"إرفاق المستندات\",\n            \"submit_request\": \"إرسال الطلب\",\n            \"requests_subtitle_applicant\": \"إدارة طلبات المساعدة الخاصة بك\",\n            \"requests_subtitle_reviewer\": \"مراجعة جميع طلبات المساعدة\",\n            \"search_requests_placeholder\": \"البحث في الطلبات...\",\n            \"request_number\": \"رقم الطلب\",\n            \"submission_date\": \"تاريخ التقديم\",\n            \"processing_stages\": \"مراحل المعالجة\",\n            \"no_requests\": \"لا توجد طلبات\",\n            \"no_requests_search\": \"لم يتم العثور على طلبات تطابق البحث\",\n            \"no_requests_applicant\": \"لم تقم بتقديم أي طلبات بعد\",\n            \"no_requests_reviewer\": \"لا توجد طلبات للمراجعة\",\n            \"submit_new_request\": \"تقديم طلب جديد\",\n            // Status\n            \"draft\": \"مسودة\",\n            \"submitted\": \"مرسل\",\n            // Workflow Stages\n            \"reception_review\": \"مراجعة الاستقبال\",\n            \"researcher_review\": \"مراجعة الباحث\",\n            \"banking_expert_review\": \"مراجعة الخبير المصرفي\",\n            \"department_head_review\": \"مراجعة رئيس القسم\",\n            \"admin_manager_review\": \"مراجعة مدير الإدارة\",\n            \"minister_review\": \"مراجعة الوزير\",\n            // Actions\n            \"approve\": \"موافقة\",\n            \"reject\": \"رفض\",\n            \"return\": \"إرجاع\",\n            \"save\": \"حفظ\",\n            \"edit\": \"تعديل\",\n            \"delete\": \"حذف\",\n            \"view\": \"عرض\",\n            \"download\": \"تحميل\",\n            \"upload\": \"رفع\",\n            // Common\n            \"loading\": \"جاري التحميل...\",\n            \"search\": \"بحث\",\n            \"filter\": \"تصفية\",\n            \"date\": \"التاريخ\",\n            \"amount\": \"المبلغ\",\n            \"status\": \"الحالة\",\n            \"notes\": \"الملاحظات\",\n            \"documents\": \"المستندات\",\n            \"history\": \"التاريخ\",\n            // Dashboard Stats\n            \"total_requests\": \"إجمالي الطلبات\",\n            \"pending_review\": \"في انتظار المراجعة\",\n            \"approved_today\": \"موافق عليها اليوم\",\n            \"rejected_today\": \"مرفوضة اليوم\",\n            \"average_processing_days\": \"متوسط أيام المعالجة\",\n            \"total_users\": \"إجمالي المستخدمين\",\n            // User Roles\n            \"zakat_applicant\": \"مقدم طلب الزكاة\",\n            \"reception_staff\": \"موظف الاستقبال\",\n            \"researcher\": \"الباحث\",\n            \"banking_expert\": \"الخبير المصرفي\",\n            \"department_head\": \"رئيس القسم\",\n            \"admin_manager\": \"مدير الإدارة\",\n            \"minister\": \"الوزير\",\n            \"system_admin\": \"مسؤول النظام\",\n            // Admin Navigation\n            \"User Management\": \"إدارة المستخدمين\",\n            \"Assistance Types\": \"أنواع المساعدات\",\n            \"System Settings\": \"إعدادات النظام\",\n            // Admin System\n            \"admin_dashboard\": \"لوحة تحكم الإدارة\",\n            \"system_administration\": \"إدارة النظام\",\n            \"admin_access_required\": \"يتطلب صلاحية المدير\",\n            \"authentication_required\": \"يتطلب تسجيل الدخول\",\n            \"please_login\": \"يرجى تسجيل الدخول\",\n            \"back_to_dashboard\": \"العودة إلى لوحة التحكم\",\n            // Admin Navigation\n            \"admin_dashboard_nav\": \"لوحة تحكم الإدارة\",\n            \"user_management\": \"إدارة المستخدمين\",\n            \"assistance_types\": \"أنواع المساعدات\",\n            \"workflow_management\": \"إدارة سير العمل\",\n            \"document_configuration\": \"إعدادات المستندات\",\n            \"distribution_rules\": \"قواعد التوزيع\",\n            \"system_settings\": \"إعدادات النظام\",\n            \"audit_trail\": \"سجل المراجعة\",\n            \"data_management\": \"إدارة البيانات\",\n            // Admin Descriptions\n            \"system_administration_overview\": \"نظرة عامة على إدارة النظام\",\n            \"manage_user_accounts_roles\": \"إدارة حسابات المستخدمين والأدوار\",\n            \"configure_aid_types_eligibility\": \"تكوين أنواع المساعدات ومعايير الأهلية\",\n            \"configure_approval_workflows\": \"تكوين سير عمل الموافقات\",\n            \"manage_document_requirements\": \"إدارة متطلبات المستندات\",\n            \"configure_application_distribution\": \"تكوين توزيع الطلبات\",\n            \"general_system_configuration\": \"إعدادات النظام العامة\",\n            \"view_system_activity_logs\": \"عرض سجلات نشاط النظام\",\n            \"import_export_backup\": \"استيراد/تصدير ونسخ احتياطي\",\n            // Beneficiary Management\n            \"beneficiary_management\": \"إدارة المستفيدين\",\n            \"beneficiary_management_desc\": \"إدارة وتتبع المستفيدين من الزكاة والمساعدات\",\n            \"beneficiary_registration\": \"تسجيل مستفيد جديد\",\n            \"beneficiary_registration_desc\": \"إضافة مستفيد جديد إلى نظام إدارة الزكاة\",\n            \"beneficiary_list\": \"قائمة المستفيدين\",\n            \"beneficiary_profile\": \"الملف الشخصي للمستفيد\",\n            \"beneficiary_details\": \"تفاصيل المستفيد\",\n            \"beneficiary_not_found\": \"المستفيد غير موجود\",\n            \"beneficiary_not_found_desc\": \"لم يتم العثور على المستفيد المطلوب\",\n            \"back_to_beneficiaries\": \"العودة إلى قائمة المستفيدين\",\n            // Beneficiary Stats\n            \"approved_beneficiaries\": \"المعتمدين\",\n            \"under_review_beneficiaries\": \"قيد المراجعة\",\n            \"total_distributions\": \"إجمالي التوزيعات\",\n            \"average_distribution\": \"متوسط التوزيع\",\n            \"pending_verification_count\": \"في انتظار التحقق\",\n            \"needs_review\": \"يحتاج إلى مراجعة\",\n            \"of_total\": \"من الإجمالي\",\n            \"per_beneficiary\": \"للمستفيد\",\n            // Search and Filters\n            \"search_and_filter\": \"البحث والتصفية\",\n            \"search_placeholder\": \"البحث بالاسم، رقم الهوية، أو رقم الهاتف...\",\n            \"filter_by_status\": \"تصفية بالحالة\",\n            \"filter_by_category\": \"تصفية بالفئة\",\n            \"all_statuses\": \"جميع الحالات\",\n            \"all_categories\": \"جميع الفئات\",\n            \"no_results_found\": \"لا توجد نتائج مطابقة لمعايير البحث\",\n            \"showing_results\": \"عرض {{count}} من أصل {{total}} مستفيد\",\n            \"export\": \"تصدير\",\n            // Beneficiary Status\n            \"suspended\": \"معلق\",\n            \"inactive\": \"غير نشط\",\n            // Zakat Categories\n            \"fuqara\": \"الفقراء\",\n            \"masakin\": \"المساكين\",\n            \"amilin\": \"العاملين عليها\",\n            \"muallafah\": \"المؤلفة قلوبهم\",\n            \"riqab\": \"في الرقاب\",\n            \"gharimin\": \"الغارمين\",\n            \"fisabilillah\": \"في سبيل الله\",\n            \"ibnus_sabil\": \"ابن السبيل\",\n            \"primary_category\": \"الفئة الأساسية\",\n            // Table Headers\n            \"name\": \"الاسم\",\n            \"total_received\": \"إجمالي المستلم\",\n            // Actions\n            \"view_profile\": \"عرض الملف الشخصي\",\n            \"manage_case\": \"إدارة الحالة\",\n            \"generate_voucher\": \"إنشاء قسيمة\",\n            \"back\": \"العودة\",\n            // Profile Tabs\n            \"overview\": \"نظرة عامة\",\n            \"personal_details\": \"البيانات الشخصية\",\n            \"eligibility_verification\": \"الأهلية والتحقق\",\n            \"case_management\": \"إدارة الحالة\",\n            \"distribution_history\": \"سجل التوزيعات\",\n            \"family\": \"الأسرة\",\n            // Profile Details\n            \"eligibility_score\": \"نقاط الأهلية\",\n            \"high_score\": \"درجة عالية\",\n            \"family_size\": \"حجم الأسرة\",\n            \"dependents\": \"معالين\",\n            \"last_distribution\": \"آخر توزيع\",\n            \"no_distribution\": \"لا يوجد\",\n            \"not_distributed_yet\": \"لم يتم التوزيع بعد\",\n            // Contact Information\n            \"contact_info\": \"معلومات الاتصال\",\n            \"account_status\": \"حالة الحساب\",\n            \"current_status\": \"الحالة الحالية\",\n            \"registration_date\": \"تاريخ التسجيل\",\n            \"next_review\": \"المراجعة القادمة\",\n            // Personal Information\n            \"personal_information\": \"البيانات الشخصية\",\n            \"basic_information\": \"المعلومات الأساسية\",\n            \"name_arabic\": \"الاسم بالعربية\",\n            \"name_english\": \"الاسم بالإنجليزية\",\n            \"date_of_birth\": \"تاريخ الميلاد\",\n            \"gender\": \"الجنس\",\n            \"male\": \"ذكر\",\n            \"female\": \"أنثى\",\n            // Family Members\n            \"family_members\": \"أفراد الأسرة\",\n            \"no_family_info\": \"لا توجد معلومات عن أفراد الأسرة\",\n            \"dependent\": \"معال\",\n            \"special_needs\": \"احتياجات خاصة\",\n            \"relationship\": \"القرابة\",\n            \"age\": \"العمر\",\n            \"years\": \"سنة\",\n            \"son\": \"ابن\",\n            \"daughter\": \"ابنة\",\n            \"mother\": \"أم\",\n            \"father\": \"أب\",\n            // Documents\n            \"no_documents\": \"لا توجد مستندات مرفوعة\",\n            \"verified\": \"محقق\",\n            \"pending_verification\": \"في انتظار التحقق\",\n            \"upload_date\": \"تاريخ الرفع\",\n            // Coming Soon\n            \"coming_soon\": \"قريباً\",\n            \"under_development\": \"قيد التطوير\",\n            \"registration_form_coming\": \"نموذج التسجيل قيد التطوير\",\n            \"registration_form_desc\": \"سيتم إضافة نموذج تسجيل المستفيدين الجدد قريباً\",\n            \"will_include_features\": \"سيتضمن النموذج الميزات التالية:\",\n            \"multi_step_form\": \"نموذج متعدد الخطوات مع مؤشر التقدم\",\n            \"dual_language_input\": \"إدخال البيانات الشخصية بالعربية والإنجليزية\",\n            \"zakat_categories_selection\": \"اختيار فئات الزكاة الثمانية\",\n            \"document_upload\": \"رفع المستندات المطلوبة\",\n            \"data_validation\": \"التحقق من صحة البيانات\",\n            \"duplicate_detection\": \"كشف التكرار التلقائي\",\n            // System Information\n            \"system_name\": \"نظام إدارة الزكاة\",\n            \"system_description\": \"نظام شامل لإدارة طلبات الزكاة والمساعدات\",\n            // Dashboard\n            \"role\": \"الدور\",\n            \"dashboard_subtitle\": \"نظرة عامة شاملة على النظام\",\n            // Quick Actions Descriptions\n            \"Add a new beneficiary to the system\": \"إضافة مستفيد جديد إلى النظام\",\n            \"Find and manage existing beneficiaries\": \"البحث عن المستفيدين الحاليين وإدارتهم\",\n            \"Create distribution and analytics reports\": \"إنشاء تقارير التوزيع والتحليلات\",\n            \"Configure distribution categories and amounts\": \"تكوين فئات ومبالغ التوزيع\",\n            \"Manage system users and permissions\": \"إدارة مستخدمي النظام والصلاحيات\",\n            \"Configure system preferences\": \"تكوين تفضيلات النظام\",\n            // Recent Activity\n            \"New beneficiary registered\": \"تم تسجيل مستفيد جديد\",\n            \"Application approved\": \"تم الموافقة على الطلب\",\n            \"Zakat distributed\": \"تم توزيع الزكاة\",\n            \"Pending review\": \"في انتظار المراجعة\",\n            \"Ahmed Mohammed Al-Rashid has been registered\": \"تم تسجيل أحمد محمد الراشد\",\n            \"Fatima Al-Zahra application approved for Zakat distribution\": \"تم الموافقة على طلب فاطمة الزهراء لتوزيع الزكاة\",\n            \"5,000 SAR distributed to 10 beneficiaries\": \"تم توزيع 5,000 ريال سعودي على 10 مستفيدين\",\n            \"3 applications require case manager review\": \"3 طلبات تتطلب مراجعة مدير الحالة\",\n            \"Reception Staff\": \"موظف الاستقبال\",\n            \"Case Manager\": \"مدير الحالة\",\n            \"Finance Manager\": \"مدير المالية\",\n            \"System\": \"النظام\",\n            // Authentication Messages\n            \"create_new_account\": \"إنشاء حساب جديد\",\n            \"sign_in_to_account\": \"تسجيل الدخول إلى حسابك\",\n            \"choose_login_method\": \"اختر طريقة تسجيل الدخول المناسبة\",\n            \"verifying\": \"جاري التحقق...\",\n            \"error\": \"خطأ\",\n            \"passwords_not_match\": \"كلمات المرور غير متطابقة\",\n            \"account_created_success\": \"تم إنشاء الحساب بنجاح\",\n            \"wait_admin_approval\": \"يرجى انتظار موافقة الإدارة على حسابك\",\n            \"account_creation_error\": \"خطأ في إنشاء الحساب\",\n            \"unexpected_error\": \"حدث خطأ غير متوقع\",\n            \"error_during_creation\": \"حدث خطأ أثناء إنشاء الحساب\",\n            \"invalid_credentials\": \"البيانات المدخلة غير صحيحة\",\n            \"welcome_to_system\": \"مرحباً بك في نظام إدارة الزكاة\",\n            \"error_during_login\": \"حدث خطأ أثناء تسجيل الدخول\",\n            // Account Status\n            \"account_pending_approval\": \"حسابك في انتظار الموافقة\",\n            \"wait_admin_approval_desc\": \"يرجى انتظار موافقة الإدارة على حسابك\",\n            // Dashboard\n            \"assigned_tasks\": \"المهام المخصصة لك\",\n            \"total_requests_desc\": \"إجمالي الطلبات\",\n            \"pending_review_desc\": \"في انتظار المراجعة\",\n            \"approved_today_desc\": \"موافق عليها اليوم\",\n            \"avg_processing_days_desc\": \"متوسط أيام المعالجة\",\n            // Reports\n            \"no_reports_access\": \"ليس لديك صلاحية للوصول إلى التقارير\",\n            \"monthly_report\": \"التقرير الشهري\",\n            \"monthly_stats_desc\": \"إحصائيات الطلبات والموافقات الشهرية\",\n            \"requests_label\": \"الطلبات\",\n            \"approved_label\": \"موافق\",\n            \"rejected_label\": \"مرفوض\",\n            // Requests\n            \"back_button\": \"العودة\",\n            \"request_details\": \"تفاصيل الطلب\",\n            \"download_decision\": \"تحميل القرار\",\n            // Gender and Personal Info\n            \"gender_label\": \"الجنس\",\n            \"male_label\": \"ذكر\",\n            \"female_label\": \"أنثى\",\n            \"marital_status_label\": \"الحالة الاجتماعية\",\n            \"married_label\": \"متزوج\",\n            \"single_label\": \"أعزب\",\n            \"divorced_label\": \"مطلق\",\n            \"widowed_label\": \"أرمل\",\n            // Common UI Text\n            \"or\": \"أو\",\n            \"no_account\": \"ليس لديك حساب؟\",\n            \"demo_accounts\": \"حسابات تجريبية:\",\n            \"applicant\": \"مقدم طلب:\",\n            \"staff_member\": \"موظف:\",\n            // Access Control\n            \"access_denied\": \"غير مصرح\",\n            \"no_beneficiary_access\": \"ليس لديك صلاحية للوصول إلى إدارة المستفيدين\",\n            \"no_registration_access\": \"ليس لديك صلاحية لتسجيل مستفيدين جدد\",\n            // Multi-Step Form\n            \"step\": \"خطوة\",\n            \"of\": \"من\",\n            \"complete\": \"مكتمل\",\n            \"optional\": \"اختياري\",\n            \"previous\": \"السابق\",\n            \"next\": \"التالي\",\n            \"save_draft\": \"حفظ مسودة\",\n            \"submit\": \"إرسال\",\n            // Form Steps\n            \"personal_details_step\": \"البيانات الشخصية\",\n            \"contact_information_step\": \"معلومات الاتصال\",\n            \"eligibility_criteria_step\": \"معايير الأهلية\",\n            \"documentation_upload_step\": \"رفع المستندات\",\n            \"review_submit_step\": \"مراجعة وإرسال\",\n            // Form Validation\n            \"field_required\": \"هذا الحقل مطلوب\",\n            \"invalid_email\": \"عنوان البريد الإلكتروني غير صحيح\",\n            \"invalid_phone\": \"رقم الهاتف غير صحيح\",\n            \"invalid_national_id\": \"رقم الهوية الوطنية غير صحيح\",\n            \"min_length\": \"يجب أن يكون الحد الأدنى {{min}} أحرف\",\n            \"max_length\": \"يجب أن لا يتجاوز {{max}} حرف\",\n            \"invalid_date\": \"التاريخ غير صحيح\",\n            \"future_date_not_allowed\": \"لا يمكن أن يكون التاريخ في المستقبل\",\n            \"invalid_arabic_name\": \"يجب أن يحتوي الاسم على أحرف عربية فقط\",\n            \"invalid_english_name\": \"يجب أن يحتوي الاسم على أحرف إنجليزية فقط\",\n            \"invalid_age\": \"العمر غير صحيح\",\n            \"invalid_postal_code\": \"الرمز البريدي غير صحيح\",\n            \"select_at_least_one_category\": \"يجب اختيار فئة واحدة على الأقل\",\n            \"min_family_size\": \"حجم الأسرة يجب أن يكون 1 على الأقل\",\n            \"max_family_size\": \"حجم الأسرة لا يمكن أن يتجاوز 20\",\n            \"min_dependents\": \"عدد المعالين لا يمكن أن يكون سالباً\",\n            \"max_dependents\": \"عدد المعالين لا يمكن أن يتجاوز 19\",\n            \"invalid_income\": \"الدخل الشهري غير صحيح\",\n            \"max_income\": \"الدخل الشهري لا يمكن أن يتجاوز 50,000 ريال\",\n            \"upload_at_least_one_document\": \"يجب رفع مستند واحد على الأقل\",\n            \"national_id_required\": \"رفع صورة الهوية الوطنية مطلوب\",\n            \"terms_must_be_accepted\": \"يجب الموافقة على الشروط والأحكام\",\n            \"data_accuracy_must_be_confirmed\": \"يجب تأكيد صحة البيانات\",\n            \"privacy_policy_must_be_accepted\": \"يجب الموافقة على سياسة الخصوصية\",\n            // Personal Details Form\n            \"personal_details_description\": \"أدخل المعلومات الشخصية الأساسية للمستفيد\",\n            \"enter_name_arabic\": \"أدخل الاسم الكامل بالعربية\",\n            \"enter_name_english\": \"أدخل الاسم الكامل بالإنجليزية\",\n            \"name_arabic_description\": \"الاسم كما هو مكتوب في الهوية الوطنية\",\n            \"name_english_description\": \"الاسم بالأحرف الإنجليزية\",\n            \"enter_national_id\": \"أدخل رقم الهوية الوطنية\",\n            \"national_id_description\": \"رقم الهوية الوطنية السعودية (10 أرقام)\",\n            \"pick_date\": \"اختر التاريخ\",\n            \"date_of_birth_description\": \"تاريخ الميلاد كما هو مكتوب في الهوية\",\n            \"select_gender\": \"اختر الجنس\",\n            \"select_marital_status\": \"اختر الحالة الاجتماعية\",\n            // Contact Information Form\n            \"contact_information_description\": \"أدخل معلومات الاتصال والعنوان\",\n            \"enter_phone_number\": \"أدخل رقم الهاتف\",\n            \"phone_number_description\": \"رقم الهاتف المحمول (يبدأ بـ 05 أو +966)\",\n            \"enter_email\": \"أدخل البريد الإلكتروني\",\n            \"email_description\": \"البريد الإلكتروني للتواصل (اختياري)\",\n            \"enter_address\": \"أدخل العنوان التفصيلي\",\n            \"address_description\": \"العنوان الكامل بما في ذلك الحي والشارع\",\n            \"enter_city\": \"أدخل المدينة\",\n            \"city_description\": \"المدينة أو المحافظة\",\n            \"select_region\": \"اختر المنطقة\",\n            \"region_description\": \"المنطقة الإدارية في المملكة\",\n            \"enter_postal_code\": \"أدخل الرمز البريدي\",\n            \"postal_code_description\": \"الرمز البريدي (5 أرقام)\",\n            // Eligibility Criteria Form\n            \"islamic_compliance_notice\": \"إشعار الامتثال الإسلامي\",\n            \"zakat_categories_description\": \"يتم تصنيف المستفيدين وفقاً للفئات الثمانية للزكاة المحددة في الشريعة الإسلامية\",\n            \"select_applicable_categories\": \"اختر الفئات المناسبة للمستفيد\",\n            \"applicable_categories\": \"الفئات المناسبة\",\n            \"select_primary_category\": \"اختر الفئة الأساسية\",\n            \"primary_category_description\": \"الفئة الأساسية التي ينتمي إليها المستفيد\",\n            \"family_financial_info\": \"المعلومات الأسرية والمالية\",\n            \"family_financial_description\": \"معلومات حول حجم الأسرة والوضع المالي\",\n            \"family_size_description\": \"العدد الإجمالي لأفراد الأسرة\",\n            \"dependents_description\": \"عدد الأشخاص المعالين\",\n            \"monthly_income_description\": \"الدخل الشهري الإجمالي بالريال السعودي\",\n            \"has_special_needs\": \"يوجد احتياجات خاصة\",\n            \"special_needs_description\": \"هل يوجد أحد من أفراد الأسرة لديه احتياجات خاصة؟\",\n            \"special_needs_details\": \"تفاصيل الاحتياجات الخاصة\",\n            \"describe_special_needs\": \"اوصف الاحتياجات الخاصة بالتفصيل\",\n            \"special_needs_details_description\": \"وصف مفصل للاحتياجات الخاصة أو الظروف الصحية\",\n            // Zakat Category Descriptions\n            \"fuqara_description\": \"الفقراء - الذين لا يملكون ما يكفي لسد حاجاتهم الأساسية\",\n            \"masakin_description\": \"المساكين - الذين يملكون أقل من نصف كفايتهم\",\n            \"amilin_description\": \"العاملين عليها - الذين يعملون في جمع وتوزيع الزكاة\",\n            \"muallafah_description\": \"المؤلفة قلوبهم - الذين يُراد تأليف قلوبهم للإسلام\",\n            \"riqab_description\": \"في الرقاب - لتحرير العبيد والأسرى\",\n            \"gharimin_description\": \"الغارمين - المدينين الذين لا يستطيعون سداد ديونهم\",\n            \"fisabilillah_description\": \"في سبيل الله - للجهاد والأعمال الخيرية\",\n            \"ibnus_sabil_description\": \"ابن السبيل - المسافر المنقطع عن بلده\",\n            // Documentation Upload Form\n            \"document_requirements\": \"متطلبات المستندات\",\n            \"document_requirements_description\": \"يرجى رفع المستندات المطلوبة لإتمام عملية التسجيل\",\n            \"required_document_missing\": \"مستند مطلوب مفقود\",\n            \"national_id_document_required\": \"رفع صورة الهوية الوطنية مطلوب لإتمام التسجيل\",\n            \"upload_documents\": \"رفع المستندات\",\n            \"upload_documents_description\": \"اسحب وأفلت الملفات هنا أو انقر للتصفح\",\n            \"drag_drop_files\": \"اسحب وأفلت الملفات هنا\",\n            \"drop_files_here\": \"أفلت الملفات هنا\",\n            \"or_click_to_browse\": \"أو انقر للتصفح\",\n            \"supported_formats\": \"الصيغ المدعومة\",\n            \"uploaded_documents\": \"المستندات المرفوعة\",\n            \"uploading\": \"جاري الرفع\",\n            \"required\": \"مطلوب\",\n            \"national_id_document\": \"صورة الهوية الوطنية\",\n            \"income_certificate\": \"شهادة الدخل\",\n            \"family_card\": \"كرت العائلة\",\n            \"medical_report\": \"التقرير الطبي\",\n            \"other_document\": \"مستند آخر\",\n            \"additional_notes\": \"ملاحظات إضافية\",\n            \"additional_notes_description\": \"أي معلومات إضافية تود إضافتها\",\n            \"enter_additional_notes\": \"أدخل أي ملاحظات إضافية\",\n            \"additional_notes_help\": \"معلومات إضافية قد تساعد في تقييم الطلب\",\n            // Review and Submit Form\n            \"review_information\": \"مراجعة المعلومات\",\n            \"review_information_description\": \"يرجى مراجعة جميع المعلومات المدخلة قبل الإرسال\",\n            \"not_provided\": \"غير مقدم\",\n            \"no_documents_uploaded\": \"لم يتم رفع أي مستندات\",\n            \"terms_and_conditions\": \"الشروط والأحكام\",\n            \"terms_conditions_description\": \"يرجى قراءة والموافقة على الشروط والأحكام\",\n            \"accept_terms_conditions\": \"أوافق على الشروط والأحكام\",\n            \"terms_conditions_text\": \"لقد قرأت وفهمت وأوافق على شروط وأحكام نظام إدارة الزكاة\",\n            \"confirm_data_accuracy\": \"أؤكد صحة البيانات\",\n            \"data_accuracy_text\": \"أؤكد أن جميع المعلومات المقدمة صحيحة ودقيقة\",\n            \"accept_privacy_policy\": \"أوافق على سياسة الخصوصية\",\n            \"privacy_policy_text\": \"أوافق على سياسة الخصوصية وطريقة التعامل مع البيانات الشخصية\",\n            // Enhanced Validation Messages\n            \"min_value\": \"يجب أن تكون القيمة {{min}} على الأقل\",\n            \"max_value\": \"يجب أن لا تتجاوز القيمة {{max}}\",\n            \"validation_error\": \"خطأ في التحقق من صحة البيانات\",\n            \"age_below_18_warning\": \"العمر أقل من 18 سنة - قد يتطلب موافقة ولي الأمر\",\n            \"age_above_100_warning\": \"العمر أكبر من 100 سنة - يرجى التحقق من التاريخ\",\n            \"dependents_family_size_warning\": \"عدد المعالين يجب أن يكون أقل من حجم الأسرة\",\n            \"high_income_warning\": \"الدخل مرتفع - قد لا يكون مؤهلاً للزكاة\",\n            \"duplicate_beneficiary_found\": \"تم العثور على مستفيد مشابه في النظام\",\n            \"duplicate_national_id\": \"رقم الهوية الوطنية مسجل مسبقاً\",\n            \"duplicate_phone\": \"رقم الهاتف مسجل مسبقاً\",\n            \"duplicate_email\": \"البريد الإلكتروني مسجل مسبقاً\",\n            \"similar_name_found\": \"تم العثور على اسم مشابه في النظام\",\n            // Accessibility Messages\n            \"form_has_error\": \"النموذج يحتوي على خطأ واحد\",\n            \"form_has_errors\": \"النموذج يحتوي على {{count}} أخطاء\",\n            \"step_announcement\": \"الخطوة {{current}} من {{total}}: {{title}}\",\n            \"progress_announcement\": \"تم إكمال {{percentage}}% من النموذج\",\n            \"required_field\": \"حقل مطلوب\",\n            \"optional_field\": \"حقل اختياري\",\n            \"error_in_field\": \"خطأ في الحقل\",\n            \"field_description\": \"وصف الحقل\",\n            \"form_navigation\": \"التنقل في النموذج\",\n            \"skip_to_content\": \"تخطي إلى المحتوى\",\n            \"skip_to_navigation\": \"تخطي إلى التنقل\",\n            // Final Implementation Messages\n            \"draft_saved_successfully\": \"تم حفظ المسودة بنجاح\",\n            \"error_saving_draft\": \"خطأ في حفظ المسودة\",\n            \"duplicate_check_failed\": \"فشل في فحص التكرار\",\n            \"beneficiary_registered_successfully\": \"تم تسجيل المستفيد بنجاح\",\n            \"beneficiary_registration_success_description\": \"تم إضافة المستفيد إلى النظام وسيتم مراجعة الطلب\",\n            \"error_submitting_form\": \"خطأ في إرسال النموذج\",\n            \"draft_loaded\": \"تم تحميل المسودة المحفوظة\",\n            \"submitting_registration\": \"جاري إرسال التسجيل\",\n            \"please_wait\": \"يرجى الانتظار...\",\n            \"beneficiary_registration_description\": \"قم بتسجيل مستفيد جديد في نظام إدارة الزكاة\",\n            // Assistance Types Management\n            \"assistance_types_management\": \"إدارة أنواع المساعدات\",\n            \"assistance_types_management_desc\": \"تكوين أنواع المساعدات ومعايير الأهلية والمتطلبات\",\n            \"add_new_type\": \"إضافة نوع جديد\",\n            \"create_assistance_type\": \"إنشاء نوع مساعدة\",\n            \"edit_assistance_type\": \"تعديل نوع المساعدة\",\n            \"assistance_type_details\": \"تفاصيل نوع المساعدة\",\n            \"basic_info_section\": \"المعلومات الأساسية\",\n            \"configure_basic_details\": \"تكوين التفاصيل الأساسية لنوع المساعدة\",\n            \"assistance_name_arabic\": \"الاسم بالعربية\",\n            \"assistance_name_english\": \"الاسم بالإنجليزية\",\n            \"description_arabic\": \"الوصف بالعربية\",\n            \"description_english\": \"الوصف بالإنجليزية\",\n            \"maximum_amount\": \"الحد الأقصى للمبلغ\",\n            \"maximum_amount_sar\": \"الحد الأقصى للمبلغ (ريال سعودي)\",\n            \"category\": \"الفئة\",\n            \"active_status\": \"الحالة النشطة\",\n            \"enable_assistance_type\": \"تفعيل نوع المساعدة للطلبات\",\n            \"required_documents\": \"المستندات المطلوبة\",\n            \"configure_required_documents\": \"تكوين المستندات المطلوبة لنوع المساعدة\",\n            \"add_document\": \"إضافة مستند\",\n            \"no_documents_configured\": \"لم يتم تكوين أي مستندات بعد\",\n            \"click_add_document\": 'انقر على \"إضافة مستند\" للبدء',\n            \"document_name_arabic\": \"اسم المستند بالعربية\",\n            \"document_name_english\": \"اسم المستند بالإنجليزية\",\n            \"is_required\": \"مطلوب\",\n            \"max_size_kb\": \"الحد الأقصى للحجم (كيلوبايت)\",\n            \"accepted_formats\": \"الصيغ المقبولة\",\n            \"total_types\": \"إجمالي الأنواع\",\n            \"active_types\": \"الأنواع النشطة\",\n            \"inactive_types\": \"الأنواع غير النشطة\",\n            \"max_amount\": \"أقصى مبلغ\",\n            \"showing_types\": \"عرض {{count}} من {{total}} أنواع المساعدات\",\n            \"no_assistance_types\": \"لا توجد أنواع مساعدات\",\n            \"search_assistance_types\": \"البحث في أنواع المساعدات...\",\n            \"view_details\": \"عرض التفاصيل\",\n            \"activate\": \"تفعيل\",\n            \"deactivate\": \"إلغاء التفعيل\",\n            \"assistance_type_not_found\": \"نوع المساعدة غير موجود\",\n            \"assistance_type_not_found_desc\": \"لم يتم العثور على نوع المساعدة المطلوب\",\n            \"back_to_assistance_types\": \"العودة إلى أنواع المساعدات\",\n            \"create_new_assistance_type\": \"إنشاء نوع مساعدة جديد\",\n            \"configure_assistance_type\": \"تكوين نوع مساعدة جديد مع معايير الأهلية والمتطلبات\",\n            \"back_to_details\": \"العودة إلى التفاصيل\",\n            \"modify_assistance_type\": \"تعديل تكوين نوع المساعدة\",\n            \"update_assistance_type\": \"تحديث نوع المساعدة\",\n            \"assistance_type_created\": \"تم إنشاء نوع المساعدة بنجاح\",\n            \"assistance_type_updated\": \"تم تحديث نوع المساعدة بنجاح\",\n            \"failed_create_assistance_type\": \"فشل في إنشاء نوع المساعدة\",\n            \"failed_update_assistance_type\": \"فشل في تحديث نوع المساعدة\",\n            \"cancel\": \"إلغاء\",\n            \"create\": \"إنشاء\",\n            \"update\": \"تحديث\",\n            \"saving\": \"جاري الحفظ...\",\n            \"eligibility_criteria\": \"معايير الأهلية\",\n            \"usage_statistics\": \"إحصائيات الاستخدام\",\n            \"total_applications\": \"إجمالي الطلبات\",\n            \"approved\": \"موافق عليها\",\n            \"rejected\": \"مرفوضة\",\n            // Common Admin Terms\n            \"manage\": \"إدارة\",\n            \"configure\": \"تكوين\",\n            \"actions\": \"الإجراءات\",\n            \"details\": \"التفاصيل\",\n            \"admin_overview\": \"نظرة عامة\",\n            \"admin_statistics\": \"الإحصائيات\",\n            \"all_operational\": \"جميعها تعمل\",\n            \"good\": \"جيد\",\n            \"all_systems_operational\": \"جميع الأنظمة تعمل\",\n            \"recently_updated\": \"محدثة مؤخراً\",\n            \"from_last_month\": \"من الشهر الماضي\",\n            \"documents_count\": \"{{count}} مستندات\",\n            \"uses\": \"استخدامات\",\n            \"formats\": \"الصيغ\",\n            \"size\": \"الحجم\",\n            \"mb\": \"ميجابايت\",\n            \"status_active\": \"نشط\",\n            \"status_inactive\": \"غير نشط\",\n            \"configure_requirements\": \"تكوين المتطلبات\",\n            \"advanced_filters\": \"مرشحات متقدمة\",\n            \"most_used\": \"الأكثر استخداماً\",\n            \"total_usage\": \"إجمالي الاستخدام\"\n        }\n    },\n    en: {\n        translation: {\n            // Navigation\n            \"dashboard\": \"Dashboard\",\n            \"profile\": \"Profile\",\n            \"requests\": \"Requests\",\n            \"tasks\": \"Tasks\",\n            \"reports\": \"Reports\",\n            \"settings\": \"Settings\",\n            \"logout\": \"Logout\",\n            // Authentication\n            \"login\": \"Login\",\n            \"register\": \"Register\",\n            \"email\": \"Email\",\n            \"password\": \"Password\",\n            \"full_name\": \"Full Name\",\n            \"national_id\": \"National ID\",\n            \"phone_number\": \"Phone Number\",\n            \"login_tawtheeq\": \"Login with Tawtheeq\",\n            \"login_success\": \"Login successful\",\n            \"login_error\": \"Invalid credentials\",\n            // Dashboard Enhancements\n            \"welcome\": \"Welcome\",\n            \"overview_status\": \"Overview of\",\n            \"system_overview\": \"system status\",\n            \"your_requests\": \"your requests\",\n            \"total_beneficiaries\": \"Total Beneficiaries\",\n            \"total_zakat_distributed\": \"Total Zakat Distributed\",\n            \"pending_applications\": \"Pending Applications\",\n            \"active_distributions\": \"Active Distributions\",\n            \"quick_actions\": \"Quick Actions\",\n            \"register_new_beneficiary\": \"Register New Beneficiary\",\n            \"search_beneficiaries\": \"Search Beneficiaries\",\n            \"generate_report\": \"Generate Report\",\n            \"manage_distributions\": \"Manage Distributions\",\n            \"recent_activity\": \"Recent Activity\",\n            \"distribution_overview\": \"Distribution Overview\",\n            \"monthly_distributions\": \"Monthly Distributions\",\n            \"beneficiary_categories\": \"Beneficiary Categories\",\n            \"view_all\": \"View All\",\n            \"this_month\": \"This Month\",\n            \"last_month\": \"Last Month\",\n            \"last_week\": \"Last Week\",\n            \"this_week\": \"This Week\",\n            \"increase\": \"increase\",\n            \"decrease\": \"decrease\",\n            \"from\": \"from\",\n            \"sar\": \"SAR\",\n            \"beneficiaries\": \"beneficiaries\",\n            \"applications\": \"applications\",\n            \"distributions\": \"distributions\",\n            \"completed\": \"Completed\",\n            \"pending\": \"Pending\",\n            \"warning\": \"Warning\",\n            // Profile\n            \"personal_profile\": \"Personal Profile\",\n            \"basic_info\": \"Basic Information\",\n            \"family_info\": \"Family Information\",\n            \"employment_info\": \"Employment Information\",\n            \"financial_info\": \"Financial Information\",\n            \"marital_status\": \"Marital Status\",\n            \"single\": \"Single\",\n            \"married\": \"Married\",\n            \"divorced\": \"Divorced\",\n            \"widowed\": \"Widowed\",\n            // Assistance Requests\n            \"assistance_request\": \"Assistance Request\",\n            \"new_request\": \"New Request\",\n            \"request_type\": \"Request Type\",\n            \"requested_amount\": \"Requested Amount\",\n            \"approved_amount\": \"Approved Amount\",\n            \"description\": \"Description\",\n            \"attach_documents\": \"Attach Documents\",\n            \"submit_request\": \"Submit Request\",\n            \"requests_subtitle_applicant\": \"Manage your assistance requests\",\n            \"requests_subtitle_reviewer\": \"Review all assistance requests\",\n            \"search_requests_placeholder\": \"Search requests...\",\n            \"request_number\": \"Request Number\",\n            \"submission_date\": \"Submission Date\",\n            \"processing_stages\": \"Processing Stages\",\n            \"no_requests\": \"No requests\",\n            \"no_requests_search\": \"No requests found matching search criteria\",\n            \"no_requests_applicant\": \"You have not submitted any requests yet\",\n            \"no_requests_reviewer\": \"No requests to review\",\n            \"submit_new_request\": \"Submit New Request\",\n            // Status\n            \"draft\": \"Draft\",\n            \"submitted\": \"Submitted\",\n            // Workflow Stages\n            \"reception_review\": \"Reception Review\",\n            \"researcher_review\": \"Researcher Review\",\n            \"banking_expert_review\": \"Banking Expert Review\",\n            \"department_head_review\": \"Department Head Review\",\n            \"admin_manager_review\": \"Admin Manager Review\",\n            \"minister_review\": \"Minister Review\",\n            // Actions\n            \"approve\": \"Approve\",\n            \"reject\": \"Reject\",\n            \"return\": \"Return\",\n            \"save\": \"Save\",\n            \"edit\": \"Edit\",\n            \"delete\": \"Delete\",\n            \"view\": \"View\",\n            \"download\": \"Download\",\n            \"upload\": \"Upload\",\n            // Common\n            \"loading\": \"Loading...\",\n            \"search\": \"Search\",\n            \"filter\": \"Filter\",\n            \"date\": \"Date\",\n            \"amount\": \"Amount\",\n            \"status\": \"Status\",\n            \"notes\": \"Notes\",\n            \"documents\": \"Documents\",\n            \"history\": \"History\",\n            // Dashboard Stats\n            \"total_requests\": \"Total Requests\",\n            \"pending_review\": \"Pending Review\",\n            \"approved_today\": \"Approved Today\",\n            \"rejected_today\": \"Rejected Today\",\n            \"average_processing_days\": \"Avg. Processing Days\",\n            \"total_users\": \"Total Users\",\n            // User Roles\n            \"zakat_applicant\": \"Zakat Applicant\",\n            \"reception_staff\": \"Reception Staff\",\n            \"researcher\": \"Researcher\",\n            \"banking_expert\": \"Banking Expert\",\n            \"department_head\": \"Department Head\",\n            \"admin_manager\": \"Administration Manager\",\n            \"minister\": \"Minister\",\n            \"system_admin\": \"System Administrator\",\n            // Admin Navigation\n            \"User Management\": \"User Management\",\n            \"Assistance Types\": \"Assistance Types\",\n            \"System Settings\": \"System Settings\",\n            // Admin System\n            \"admin_dashboard\": \"Admin Dashboard\",\n            \"system_administration\": \"System Administration\",\n            \"admin_access_required\": \"Admin access required\",\n            \"authentication_required\": \"Authentication required\",\n            \"please_login\": \"Please login\",\n            \"back_to_dashboard\": \"Back to Dashboard\",\n            // Admin Navigation\n            \"admin_dashboard_nav\": \"Admin Dashboard\",\n            \"user_management\": \"User Management\",\n            \"assistance_types\": \"Assistance Types\",\n            \"workflow_management\": \"Workflow Management\",\n            \"document_configuration\": \"Document Configuration\",\n            \"distribution_rules\": \"Distribution Rules\",\n            \"system_settings\": \"System Settings\",\n            \"audit_trail\": \"Audit Trail\",\n            \"data_management\": \"Data Management\",\n            // Admin Descriptions\n            \"system_administration_overview\": \"System administration overview\",\n            \"manage_user_accounts_roles\": \"Manage user accounts and roles\",\n            \"configure_aid_types_eligibility\": \"Configure aid types and eligibility\",\n            \"configure_approval_workflows\": \"Configure approval workflows\",\n            \"manage_document_requirements\": \"Manage document requirements\",\n            \"configure_application_distribution\": \"Configure application distribution\",\n            \"general_system_configuration\": \"General system configuration\",\n            \"view_system_activity_logs\": \"View system activity logs\",\n            \"import_export_backup\": \"Import/export and backup\",\n            // Beneficiary Management\n            \"beneficiary_management\": \"Beneficiary Management\",\n            \"beneficiary_management_desc\": \"Manage and track Zakat and assistance beneficiaries\",\n            \"beneficiary_registration\": \"New Beneficiary Registration\",\n            \"beneficiary_registration_desc\": \"Add a new beneficiary to the Zakat management system\",\n            \"beneficiary_list\": \"Beneficiary List\",\n            \"beneficiary_profile\": \"Beneficiary Profile\",\n            \"beneficiary_details\": \"Beneficiary Details\",\n            \"beneficiary_not_found\": \"Beneficiary Not Found\",\n            \"beneficiary_not_found_desc\": \"The requested beneficiary could not be found\",\n            \"back_to_beneficiaries\": \"Back to Beneficiaries List\",\n            // Beneficiary Stats\n            \"approved_beneficiaries\": \"Approved\",\n            \"under_review_beneficiaries\": \"Under Review\",\n            \"total_distributions\": \"Total Distributions\",\n            \"average_distribution\": \"Average Distribution\",\n            \"pending_verification_count\": \"Pending Verification\",\n            \"needs_review\": \"Needs Review\",\n            \"of_total\": \"of Total\",\n            \"per_beneficiary\": \"per Beneficiary\",\n            // Search and Filters\n            \"search_and_filter\": \"Search and Filter\",\n            \"search_placeholder\": \"Search by name, national ID, or phone number...\",\n            \"filter_by_status\": \"Filter by Status\",\n            \"filter_by_category\": \"Filter by Category\",\n            \"all_statuses\": \"All Statuses\",\n            \"all_categories\": \"All Categories\",\n            \"no_results_found\": \"No results found matching search criteria\",\n            \"showing_results\": \"Showing {{count}} of {{total}} beneficiaries\",\n            \"export\": \"Export\",\n            // Beneficiary Status\n            \"pending_verification\": \"Pending Verification\",\n            \"under_review\": \"Under Review\",\n            \"approved\": \"Approved\",\n            \"rejected\": \"Rejected\",\n            \"suspended\": \"Suspended\",\n            \"inactive\": \"Inactive\",\n            // Zakat Categories\n            \"fuqara\": \"The Poor\",\n            \"masakin\": \"The Needy\",\n            \"amilin\": \"Zakat Administrators\",\n            \"muallafah\": \"Those whose hearts are reconciled\",\n            \"riqab\": \"To free slaves/captives\",\n            \"gharimin\": \"Those in debt\",\n            \"fisabilillah\": \"In the cause of Allah\",\n            \"ibnus_sabil\": \"The wayfarer/traveler\",\n            \"primary_category\": \"Primary Category\",\n            // Table Headers\n            \"name\": \"Name\",\n            \"total_received\": \"Total Received\",\n            // Actions\n            \"view_profile\": \"View Profile\",\n            \"manage_case\": \"Manage Case\",\n            \"generate_voucher\": \"Generate Voucher\",\n            \"back\": \"Back\",\n            // Profile Tabs\n            \"overview\": \"Overview\",\n            \"personal_details\": \"Personal Details\",\n            \"eligibility_verification\": \"Eligibility & Verification\",\n            \"case_management\": \"Case Management\",\n            \"distribution_history\": \"Distribution History\",\n            \"family\": \"Family\",\n            // Profile Details\n            \"eligibility_score\": \"Eligibility Score\",\n            \"high_score\": \"High Score\",\n            \"family_size\": \"Family Size\",\n            \"dependents\": \"Dependents\",\n            \"last_distribution\": \"Last Distribution\",\n            \"no_distribution\": \"None\",\n            \"not_distributed_yet\": \"Not distributed yet\",\n            // Contact Information\n            \"contact_info\": \"Contact Information\",\n            \"account_status\": \"Account Status\",\n            \"current_status\": \"Current Status\",\n            \"registration_date\": \"Registration Date\",\n            \"next_review\": \"Next Review\",\n            // Personal Information\n            \"personal_information\": \"Personal Information\",\n            \"basic_information\": \"Basic Information\",\n            \"name_arabic\": \"Name in Arabic\",\n            \"name_english\": \"Name in English\",\n            \"date_of_birth\": \"Date of Birth\",\n            \"gender\": \"Gender\",\n            \"male\": \"Male\",\n            \"female\": \"Female\",\n            // Family Members\n            \"family_members\": \"Family Members\",\n            \"no_family_info\": \"No family information available\",\n            \"dependent\": \"Dependent\",\n            \"special_needs\": \"Special Needs\",\n            \"relationship\": \"Relationship\",\n            \"age\": \"Age\",\n            \"years\": \"years\",\n            \"son\": \"Son\",\n            \"daughter\": \"Daughter\",\n            \"mother\": \"Mother\",\n            \"father\": \"Father\",\n            // Documents\n            \"no_documents\": \"No documents uploaded\",\n            \"verified\": \"Verified\",\n            \"upload_date\": \"Upload Date\",\n            // Coming Soon\n            \"coming_soon\": \"Coming Soon\",\n            \"under_development\": \"Under Development\",\n            \"registration_form_coming\": \"Registration Form Under Development\",\n            \"registration_form_desc\": \"New beneficiary registration form will be added soon\",\n            \"will_include_features\": \"The form will include the following features:\",\n            \"multi_step_form\": \"Multi-step form with progress indicator\",\n            \"dual_language_input\": \"Personal data input in Arabic and English\",\n            \"zakat_categories_selection\": \"Selection of eight Zakat categories\",\n            \"document_upload\": \"Required document upload\",\n            \"data_validation\": \"Data validation\",\n            \"duplicate_detection\": \"Automatic duplicate detection\",\n            // System Information\n            \"system_name\": \"Zakat Management System\",\n            \"system_description\": \"Comprehensive system for managing Zakat and assistance requests\",\n            // Dashboard\n            \"role\": \"Role\",\n            \"dashboard_subtitle\": \"Comprehensive system overview\",\n            // Quick Actions Descriptions\n            \"Add a new beneficiary to the system\": \"Add a new beneficiary to the system\",\n            \"Find and manage existing beneficiaries\": \"Find and manage existing beneficiaries\",\n            \"Create distribution and analytics reports\": \"Create distribution and analytics reports\",\n            \"Configure distribution categories and amounts\": \"Configure distribution categories and amounts\",\n            \"Manage system users and permissions\": \"Manage system users and permissions\",\n            \"Configure system preferences\": \"Configure system preferences\",\n            // Recent Activity\n            \"New beneficiary registered\": \"New beneficiary registered\",\n            \"Application approved\": \"Application approved\",\n            \"Zakat distributed\": \"Zakat distributed\",\n            \"Pending review\": \"Pending review\",\n            \"Ahmed Mohammed Al-Rashid has been registered\": \"Ahmed Mohammed Al-Rashid has been registered\",\n            \"Fatima Al-Zahra application approved for Zakat distribution\": \"Fatima Al-Zahra application approved for Zakat distribution\",\n            \"5,000 SAR distributed to 10 beneficiaries\": \"5,000 SAR distributed to 10 beneficiaries\",\n            \"3 applications require case manager review\": \"3 applications require case manager review\",\n            \"Reception Staff\": \"Reception Staff\",\n            \"Case Manager\": \"Case Manager\",\n            \"Finance Manager\": \"Finance Manager\",\n            \"System\": \"System\",\n            // Authentication Messages\n            \"create_new_account\": \"Create new account\",\n            \"sign_in_to_account\": \"Sign in to your account\",\n            \"choose_login_method\": \"Choose the appropriate login method\",\n            \"verifying\": \"Verifying...\",\n            \"error\": \"Error\",\n            \"passwords_not_match\": \"Passwords do not match\",\n            \"account_created_success\": \"Account created successfully\",\n            \"wait_admin_approval\": \"Please wait for admin approval of your account\",\n            \"account_creation_error\": \"Error creating account\",\n            \"unexpected_error\": \"An unexpected error occurred\",\n            \"error_during_creation\": \"An error occurred during account creation\",\n            \"invalid_credentials\": \"Invalid credentials entered\",\n            \"welcome_to_system\": \"Welcome to the Zakat Management System\",\n            \"error_during_login\": \"An error occurred during login\",\n            // Account Status\n            \"account_pending_approval\": \"Your account is pending approval\",\n            \"wait_admin_approval_desc\": \"Please wait for admin approval of your account\",\n            // Dashboard\n            \"assigned_tasks\": \"tasks assigned to you\",\n            \"total_requests_desc\": \"Total requests\",\n            \"pending_review_desc\": \"Pending review\",\n            \"approved_today_desc\": \"Approved today\",\n            \"avg_processing_days_desc\": \"Average processing days\",\n            // Reports\n            \"no_reports_access\": \"You do not have permission to access reports\",\n            \"monthly_report\": \"Monthly Report\",\n            \"monthly_stats_desc\": \"Monthly statistics of requests and approvals\",\n            \"requests_label\": \"Requests\",\n            \"approved_label\": \"Approved\",\n            \"rejected_label\": \"Rejected\",\n            // Requests\n            \"back_button\": \"Back\",\n            \"request_details\": \"Request Details\",\n            \"download_decision\": \"Download Decision\",\n            // Gender and Personal Info\n            \"gender_label\": \"Gender\",\n            \"male_label\": \"Male\",\n            \"female_label\": \"Female\",\n            \"marital_status_label\": \"Marital Status\",\n            \"married_label\": \"Married\",\n            \"single_label\": \"Single\",\n            \"divorced_label\": \"Divorced\",\n            \"widowed_label\": \"Widowed\",\n            // Common UI Text\n            \"or\": \"or\",\n            \"no_account\": \"Don't have an account?\",\n            \"demo_accounts\": \"Demo Accounts:\",\n            \"applicant\": \"Applicant:\",\n            \"staff_member\": \"Staff Member:\",\n            // Access Control\n            \"access_denied\": \"Access Denied\",\n            \"no_beneficiary_access\": \"You do not have permission to access beneficiary management\",\n            \"no_registration_access\": \"You do not have permission to register new beneficiaries\",\n            // Multi-Step Form\n            \"step\": \"Step\",\n            \"of\": \"of\",\n            \"complete\": \"Complete\",\n            \"optional\": \"Optional\",\n            \"previous\": \"Previous\",\n            \"next\": \"Next\",\n            \"save_draft\": \"Save Draft\",\n            \"submit\": \"Submit\",\n            // Form Steps\n            \"personal_details_step\": \"Personal Details\",\n            \"contact_information_step\": \"Contact Information\",\n            \"eligibility_criteria_step\": \"Eligibility Criteria\",\n            \"documentation_upload_step\": \"Documentation Upload\",\n            \"review_submit_step\": \"Review & Submit\",\n            // Form Validation\n            \"field_required\": \"This field is required\",\n            \"invalid_email\": \"Invalid email address\",\n            \"invalid_phone\": \"Invalid phone number\",\n            \"invalid_national_id\": \"Invalid national ID\",\n            \"min_length\": \"Must be at least {{min}} characters\",\n            \"max_length\": \"Must not exceed {{max}} characters\",\n            \"invalid_date\": \"Invalid date\",\n            \"future_date_not_allowed\": \"Date cannot be in the future\",\n            \"invalid_arabic_name\": \"Name must contain only Arabic characters\",\n            \"invalid_english_name\": \"Name must contain only English characters\",\n            \"invalid_age\": \"Invalid age\",\n            \"invalid_postal_code\": \"Invalid postal code\",\n            \"select_at_least_one_category\": \"Must select at least one category\",\n            \"min_family_size\": \"Family size must be at least 1\",\n            \"max_family_size\": \"Family size cannot exceed 20\",\n            \"min_dependents\": \"Number of dependents cannot be negative\",\n            \"max_dependents\": \"Number of dependents cannot exceed 19\",\n            \"invalid_income\": \"Invalid monthly income\",\n            \"max_income\": \"Monthly income cannot exceed 50,000 SAR\",\n            \"upload_at_least_one_document\": \"Must upload at least one document\",\n            \"national_id_required\": \"National ID document upload is required\",\n            \"terms_must_be_accepted\": \"Terms and conditions must be accepted\",\n            \"data_accuracy_must_be_confirmed\": \"Data accuracy must be confirmed\",\n            \"privacy_policy_must_be_accepted\": \"Privacy policy must be accepted\",\n            // Personal Details Form\n            \"personal_details_description\": \"Enter the basic personal information for the beneficiary\",\n            \"enter_name_arabic\": \"Enter full name in Arabic\",\n            \"enter_name_english\": \"Enter full name in English\",\n            \"name_arabic_description\": \"Name as written on the national ID\",\n            \"name_english_description\": \"Name in English characters\",\n            \"enter_national_id\": \"Enter national ID number\",\n            \"national_id_description\": \"Saudi national ID number (10 digits)\",\n            \"pick_date\": \"Pick a date\",\n            \"date_of_birth_description\": \"Date of birth as written on the ID\",\n            \"select_gender\": \"Select gender\",\n            \"select_marital_status\": \"Select marital status\",\n            // Contact Information Form\n            \"contact_information_description\": \"Enter contact information and address details\",\n            \"enter_phone_number\": \"Enter phone number\",\n            \"phone_number_description\": \"Mobile phone number (starts with 05 or +966)\",\n            \"enter_email\": \"Enter email address\",\n            \"email_description\": \"Email address for communication (optional)\",\n            \"enter_address\": \"Enter detailed address\",\n            \"address_description\": \"Complete address including district and street\",\n            \"enter_city\": \"Enter city\",\n            \"city_description\": \"City or governorate\",\n            \"select_region\": \"Select region\",\n            \"region_description\": \"Administrative region in Saudi Arabia\",\n            \"enter_postal_code\": \"Enter postal code\",\n            \"postal_code_description\": \"Postal code (5 digits)\",\n            // Eligibility Criteria Form\n            \"islamic_compliance_notice\": \"Islamic Compliance Notice\",\n            \"zakat_categories_description\": \"Beneficiaries are classified according to the eight Zakat categories defined in Islamic law\",\n            \"select_applicable_categories\": \"Select applicable categories for the beneficiary\",\n            \"applicable_categories\": \"Applicable Categories\",\n            \"select_primary_category\": \"Select primary category\",\n            \"primary_category_description\": \"The primary category that the beneficiary belongs to\",\n            \"family_financial_info\": \"Family & Financial Information\",\n            \"family_financial_description\": \"Information about family size and financial situation\",\n            \"family_size_description\": \"Total number of family members\",\n            \"dependents_description\": \"Number of dependent persons\",\n            \"monthly_income_description\": \"Total monthly income in Saudi Riyals\",\n            \"has_special_needs\": \"Has special needs\",\n            \"special_needs_description\": \"Does any family member have special needs?\",\n            \"special_needs_details\": \"Special needs details\",\n            \"describe_special_needs\": \"Describe the special needs in detail\",\n            \"special_needs_details_description\": \"Detailed description of special needs or health conditions\",\n            // Zakat Category Descriptions\n            \"fuqara_description\": \"The Poor - Those who do not have enough to meet their basic needs\",\n            \"masakin_description\": \"The Needy - Those who have less than half of their sufficiency\",\n            \"amilin_description\": \"Zakat Administrators - Those who work in collecting and distributing Zakat\",\n            \"muallafah_description\": \"Those whose hearts are reconciled - Those whose hearts are to be won over to Islam\",\n            \"riqab_description\": \"To free slaves/captives - For freeing slaves and prisoners\",\n            \"gharimin_description\": \"Those in debt - Debtors who cannot pay their debts\",\n            \"fisabilillah_description\": \"In the cause of Allah - For jihad and charitable works\",\n            \"ibnus_sabil_description\": \"The wayfarer/traveler - Travelers stranded away from home\",\n            // Documentation Upload Form\n            \"document_requirements\": \"Document Requirements\",\n            \"document_requirements_description\": \"Please upload the required documents to complete the registration process\",\n            \"required_document_missing\": \"Required Document Missing\",\n            \"national_id_document_required\": \"National ID document upload is required to complete registration\",\n            \"upload_documents\": \"Upload Documents\",\n            \"upload_documents_description\": \"Drag and drop files here or click to browse\",\n            \"drag_drop_files\": \"Drag and drop files here\",\n            \"drop_files_here\": \"Drop files here\",\n            \"or_click_to_browse\": \"Or click to browse\",\n            \"supported_formats\": \"Supported formats\",\n            \"uploaded_documents\": \"Uploaded Documents\",\n            \"uploading\": \"Uploading\",\n            \"required\": \"Required\",\n            \"national_id_document\": \"National ID Document\",\n            \"income_certificate\": \"Income Certificate\",\n            \"family_card\": \"Family Card\",\n            \"medical_report\": \"Medical Report\",\n            \"other_document\": \"Other Document\",\n            \"additional_notes\": \"Additional Notes\",\n            \"additional_notes_description\": \"Any additional information you would like to add\",\n            \"enter_additional_notes\": \"Enter any additional notes\",\n            \"additional_notes_help\": \"Additional information that may help in evaluating the request\",\n            // Review and Submit Form\n            \"review_information\": \"Review Information\",\n            \"review_information_description\": \"Please review all entered information before submitting\",\n            \"not_provided\": \"Not provided\",\n            \"no_documents_uploaded\": \"No documents uploaded\",\n            \"terms_and_conditions\": \"Terms and Conditions\",\n            \"terms_conditions_description\": \"Please read and agree to the terms and conditions\",\n            \"accept_terms_conditions\": \"I accept the terms and conditions\",\n            \"terms_conditions_text\": \"I have read, understood, and agree to the terms and conditions of the Zakat Management System\",\n            \"confirm_data_accuracy\": \"I confirm data accuracy\",\n            \"data_accuracy_text\": \"I confirm that all provided information is accurate and correct\",\n            \"accept_privacy_policy\": \"I accept the privacy policy\",\n            \"privacy_policy_text\": \"I agree to the privacy policy and how personal data is handled\",\n            // Enhanced Validation Messages\n            \"min_value\": \"Value must be at least {{min}}\",\n            \"max_value\": \"Value must not exceed {{max}}\",\n            \"validation_error\": \"Validation error occurred\",\n            \"age_below_18_warning\": \"Age below 18 - may require guardian approval\",\n            \"age_above_100_warning\": \"Age above 100 - please verify the date\",\n            \"dependents_family_size_warning\": \"Number of dependents should be less than family size\",\n            \"high_income_warning\": \"High income - may not be eligible for Zakat\",\n            \"duplicate_beneficiary_found\": \"Similar beneficiary found in the system\",\n            \"duplicate_national_id\": \"National ID already registered\",\n            \"duplicate_phone\": \"Phone number already registered\",\n            \"duplicate_email\": \"Email address already registered\",\n            \"similar_name_found\": \"Similar name found in the system\",\n            // Accessibility Messages\n            \"form_has_error\": \"Form has one error\",\n            \"form_has_errors\": \"Form has {{count}} errors\",\n            \"step_announcement\": \"Step {{current}} of {{total}}: {{title}}\",\n            \"progress_announcement\": \"{{percentage}}% of form completed\",\n            \"required_field\": \"Required field\",\n            \"optional_field\": \"Optional field\",\n            \"error_in_field\": \"Error in field\",\n            \"field_description\": \"Field description\",\n            \"form_navigation\": \"Form navigation\",\n            \"skip_to_content\": \"Skip to content\",\n            \"skip_to_navigation\": \"Skip to navigation\",\n            // Final Implementation Messages\n            \"draft_saved_successfully\": \"Draft saved successfully\",\n            \"error_saving_draft\": \"Error saving draft\",\n            \"duplicate_check_failed\": \"Duplicate check failed\",\n            \"beneficiary_registered_successfully\": \"Beneficiary registered successfully\",\n            \"beneficiary_registration_success_description\": \"The beneficiary has been added to the system and the request will be reviewed\",\n            \"error_submitting_form\": \"Error submitting form\",\n            \"draft_loaded\": \"Saved draft loaded\",\n            \"submitting_registration\": \"Submitting registration\",\n            \"please_wait\": \"Please wait...\",\n            \"beneficiary_registration_description\": \"Register a new beneficiary in the Zakat Management System\",\n            // Assistance Types Management\n            \"assistance_types_management\": \"Assistance Types Management\",\n            \"assistance_types_management_desc\": \"Configure aid types, eligibility criteria, and requirements\",\n            \"add_new_type\": \"Add New Type\",\n            \"create_assistance_type\": \"Create Assistance Type\",\n            \"edit_assistance_type\": \"Edit Assistance Type\",\n            \"assistance_type_details\": \"Assistance Type Details\",\n            \"basic_info_section\": \"Basic Information\",\n            \"configure_basic_details\": \"Configure the basic details of the assistance type\",\n            \"assistance_name_arabic\": \"Name (Arabic)\",\n            \"assistance_name_english\": \"Name (English)\",\n            \"description_arabic\": \"Description (Arabic)\",\n            \"description_english\": \"Description (English)\",\n            \"maximum_amount\": \"Maximum Amount\",\n            \"maximum_amount_sar\": \"Maximum Amount (SAR)\",\n            \"category\": \"Category\",\n            \"active_status\": \"Active Status\",\n            \"enable_assistance_type\": \"Enable this assistance type for applications\",\n            \"required_documents\": \"Required Documents\",\n            \"configure_required_documents\": \"Configure documents required for this assistance type\",\n            \"add_document\": \"Add Document\",\n            \"no_documents_configured\": \"No documents configured yet\",\n            \"click_add_document\": 'Click \"Add Document\" to get started',\n            \"document_name_arabic\": \"Name (Arabic)\",\n            \"document_name_english\": \"Name (English)\",\n            \"is_required\": \"Required\",\n            \"max_size_kb\": \"Max Size (KB)\",\n            \"accepted_formats\": \"Accepted Formats\",\n            \"total_types\": \"Total Types\",\n            \"active_types\": \"Active Types\",\n            \"inactive_types\": \"Inactive Types\",\n            \"max_amount\": \"Max Amount\",\n            \"showing_types\": \"Showing {{count}} of {{total}} assistance types\",\n            \"no_assistance_types\": \"No assistance types found\",\n            \"search_assistance_types\": \"Search assistance types...\",\n            \"view_details\": \"View Details\",\n            \"activate\": \"Activate\",\n            \"deactivate\": \"Deactivate\",\n            \"assistance_type_not_found\": \"Assistance Type Not Found\",\n            \"assistance_type_not_found_desc\": \"The requested assistance type could not be found\",\n            \"back_to_assistance_types\": \"Back to Assistance Types\",\n            \"create_new_assistance_type\": \"Create New Assistance Type\",\n            \"configure_assistance_type\": \"Configure a new assistance type with eligibility criteria and requirements\",\n            \"back_to_details\": \"Back to Details\",\n            \"modify_assistance_type\": \"Modify the configuration of this assistance type\",\n            \"update_assistance_type\": \"Update Assistance Type\",\n            \"assistance_type_created\": \"Assistance type created successfully\",\n            \"assistance_type_updated\": \"Assistance type updated successfully\",\n            \"failed_create_assistance_type\": \"Failed to create assistance type\",\n            \"failed_update_assistance_type\": \"Failed to update assistance type\",\n            \"cancel\": \"Cancel\",\n            \"create\": \"Create\",\n            \"update\": \"Update\",\n            \"saving\": \"Saving...\",\n            \"eligibility_criteria\": \"Eligibility Criteria\",\n            \"usage_statistics\": \"Usage Statistics\",\n            \"total_applications\": \"Total Applications\",\n            \"approved_requests\": \"Approved\",\n            \"rejected_requests\": \"Rejected\",\n            // Common Admin Terms\n            \"manage\": \"Manage\",\n            \"configure\": \"Configure\",\n            \"actions\": \"Actions\",\n            \"details\": \"Details\",\n            \"admin_overview\": \"Overview\",\n            \"admin_statistics\": \"Statistics\",\n            \"all_operational\": \"All operational\",\n            \"good\": \"Good\",\n            \"all_systems_operational\": \"All systems operational\",\n            \"recently_updated\": \"Recently updated\",\n            \"from_last_month\": \"From last month\",\n            \"documents_count\": \"{{count}} documents\",\n            \"uses\": \"uses\",\n            \"formats\": \"Formats\",\n            \"size\": \"Size\",\n            \"mb\": \"MB\",\n            \"status_active\": \"Active\",\n            \"status_inactive\": \"Inactive\",\n            \"configure_requirements\": \"Configure Requirements\",\n            \"advanced_filters\": \"Advanced Filters\",\n            \"most_used\": \"Most Used\",\n            \"total_usage\": \"Total Usage\"\n        }\n    }\n};\n// Initialize i18n immediately with resources\ni18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"].use(i18next_browser_languagedetector__WEBPACK_IMPORTED_MODULE_2__[\"default\"]).use(react_i18next__WEBPACK_IMPORTED_MODULE_1__.initReactI18next).init({\n    resources,\n    fallbackLng: \"ar\",\n    lng: \"ar\",\n    debug: \"development\" === \"development\",\n    detection: {\n        order: [\n            \"localStorage\",\n            \"navigator\",\n            \"htmlTag\"\n        ],\n        caches: [\n            \"localStorage\"\n        ]\n    },\n    interpolation: {\n        escapeValue: false\n    }\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (i18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/i18n.ts\n");

/***/ }),

/***/ "(ssr)/./providers/i18n-provider.tsx":
/*!*************************************!*\
  !*** ./providers/i18n-provider.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nProvider: () => (/* binding */ I18nProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/i18n */ \"(ssr)/./lib/i18n.ts\");\n/* __next_internal_client_entry_do_not_use__ I18nProvider auto */ \n\n\nfunction I18nProvider({ children }) {\n    // Always render the I18nextProvider to avoid hydration mismatch\n    // The i18n instance is already initialized with resources in lib/i18n.ts\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_i18next__WEBPACK_IMPORTED_MODULE_1__.I18nextProvider, {\n        i18n: _lib_i18n__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\providers\\\\i18n-provider.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9wcm92aWRlcnMvaTE4bi1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBSStDO0FBQ2xCO0FBTXRCLFNBQVNFLGFBQWEsRUFBRUMsUUFBUSxFQUFxQjtJQUMxRCxnRUFBZ0U7SUFDaEUseUVBQXlFO0lBQ3pFLHFCQUNFLDhEQUFDSCwwREFBZUE7UUFBQ0MsTUFBTUEsaURBQUlBO2tCQUN4QkU7Ozs7OztBQUdQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBwLy4vcHJvdmlkZXJzL2kxOG4tcHJvdmlkZXIudHN4PzVkYjIiXSwic291cmNlc0NvbnRlbnQiOlsiXG4ndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBJMThuZXh0UHJvdmlkZXIgfSBmcm9tICdyZWFjdC1pMThuZXh0J1xuaW1wb3J0IGkxOG4gZnJvbSAnQC9saWIvaTE4bidcblxuaW50ZXJmYWNlIEkxOG5Qcm92aWRlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZVxufVxuXG5leHBvcnQgZnVuY3Rpb24gSTE4blByb3ZpZGVyKHsgY2hpbGRyZW4gfTogSTE4blByb3ZpZGVyUHJvcHMpIHtcbiAgLy8gQWx3YXlzIHJlbmRlciB0aGUgSTE4bmV4dFByb3ZpZGVyIHRvIGF2b2lkIGh5ZHJhdGlvbiBtaXNtYXRjaFxuICAvLyBUaGUgaTE4biBpbnN0YW5jZSBpcyBhbHJlYWR5IGluaXRpYWxpemVkIHdpdGggcmVzb3VyY2VzIGluIGxpYi9pMThuLnRzXG4gIHJldHVybiAoXG4gICAgPEkxOG5leHRQcm92aWRlciBpMThuPXtpMThufT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L0kxOG5leHRQcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkkxOG5leHRQcm92aWRlciIsImkxOG4iLCJJMThuUHJvdmlkZXIiLCJjaGlsZHJlbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./providers/i18n-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./providers/session-provider.tsx":
/*!****************************************!*\
  !*** ./providers/session-provider.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ SessionProvider auto */ \n\nfunction SessionProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\providers\\\\session-provider.tsx\",\n        lineNumber: 12,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9wcm92aWRlcnMvc2Vzc2lvbi1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRzRFO0FBT3JFLFNBQVNBLGdCQUFnQixFQUFFRSxRQUFRLEVBQXdCO0lBQ2hFLHFCQUFPLDhEQUFDRCw0REFBdUJBO2tCQUFFQzs7Ozs7O0FBQ25DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBwLy4vcHJvdmlkZXJzL3Nlc3Npb24tcHJvdmlkZXIudHN4PzIzNWEiXSwic291cmNlc0NvbnRlbnQiOlsiXG4ndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIGFzIE5leHRBdXRoU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0J1xuaW1wb3J0IHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnXG5cbmludGVyZmFjZSBTZXNzaW9uUHJvdmlkZXJQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFNlc3Npb25Qcm92aWRlcih7IGNoaWxkcmVuIH06IFNlc3Npb25Qcm92aWRlclByb3BzKSB7XG4gIHJldHVybiA8TmV4dEF1dGhTZXNzaW9uUHJvdmlkZXI+e2NoaWxkcmVufTwvTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXI+XG59XG4iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXIiLCJjaGlsZHJlbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./providers/session-provider.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"515874a36e9a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcHAvLi9hcHAvZ2xvYmFscy5jc3M/YjcwMSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjUxNTg3NGEzNmU5YVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _providers_session_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/session-provider */ \"(rsc)/./providers/session-provider.tsx\");\n/* harmony import */ var _providers_i18n_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/providers/i18n-provider */ \"(rsc)/./providers/i18n-provider.tsx\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"نظام إدارة الزكاة - Zakat Management System\",\n    description: \"نظام شامل لإدارة طلبات الزكاة والمساعدات\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().variable)} font-sans antialiased`,\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"light\",\n                enableSystem: true,\n                disableTransitionOnChange: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_session_provider__WEBPACK_IMPORTED_MODULE_2__.SessionProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_i18n_provider__WEBPACK_IMPORTED_MODULE_3__.I18nProvider, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQVFNQTtBQUxnQjtBQUN3QztBQUNOO0FBQ0c7QUFRcEQsTUFBTUksV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyxLQUFJO1FBQU1DLHdCQUF3QjtrQkFDaEQsNEVBQUNDO1lBQUtDLFdBQVcsQ0FBQyxFQUFFZCwyTEFBYyxDQUFDLHNCQUFzQixDQUFDO1lBQUVZLHdCQUF3QjtzQkFDbEYsNEVBQUNULHFFQUFhQTtnQkFDWmEsV0FBVTtnQkFDVkMsY0FBYTtnQkFDYkMsWUFBWTtnQkFDWkMseUJBQXlCOzBCQUV6Qiw0RUFBQ2xCLHdFQUFlQTs4QkFDZCw0RUFBQ0Msa0VBQVlBO2tDQUNWTTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPZiIsInNvdXJjZXMiOlsid2VicGFjazovL2FwcC8uL2FwcC9sYXlvdXQudHN4Pzk5ODgiXSwic291cmNlc0NvbnRlbnQiOlsiXG5pbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCB7IEludGVyIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ0AvcHJvdmlkZXJzL3Nlc3Npb24tcHJvdmlkZXInXG5pbXBvcnQgeyBJMThuUHJvdmlkZXIgfSBmcm9tICdAL3Byb3ZpZGVycy9pMThuLXByb3ZpZGVyJ1xuaW1wb3J0IHsgVGhlbWVQcm92aWRlciB9IGZyb20gJ0AvY29tcG9uZW50cy90aGVtZS1wcm92aWRlcidcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7XG4gIHN1YnNldHM6IFsnbGF0aW4nXSxcbiAgZGlzcGxheTogJ3N3YXAnLFxuICB2YXJpYWJsZTogJy0tZm9udC1pbnRlcidcbn0pXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAn2YbYuNin2YUg2KXYr9in2LHYqSDYp9mE2LLZg9in2KkgLSBaYWthdCBNYW5hZ2VtZW50IFN5c3RlbScsXG4gIGRlc2NyaXB0aW9uOiAn2YbYuNin2YUg2LTYp9mF2YQg2YTYpdiv2KfYsdipINi32YTYqNin2Kog2KfZhNiy2YPYp9ipINmI2KfZhNmF2LPYp9i52K/Yp9iqJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImFyXCIgZGlyPVwicnRsXCIgc3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtgJHtpbnRlci52YXJpYWJsZX0gZm9udC1zYW5zIGFudGlhbGlhc2VkYH0gc3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nPlxuICAgICAgICA8VGhlbWVQcm92aWRlclxuICAgICAgICAgIGF0dHJpYnV0ZT1cImNsYXNzXCJcbiAgICAgICAgICBkZWZhdWx0VGhlbWU9XCJsaWdodFwiXG4gICAgICAgICAgZW5hYmxlU3lzdGVtXG4gICAgICAgICAgZGlzYWJsZVRyYW5zaXRpb25PbkNoYW5nZVxuICAgICAgICA+XG4gICAgICAgICAgPFNlc3Npb25Qcm92aWRlcj5cbiAgICAgICAgICAgIDxJMThuUHJvdmlkZXI+XG4gICAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICAgIDwvSTE4blByb3ZpZGVyPlxuICAgICAgICAgIDwvU2Vzc2lvblByb3ZpZGVyPlxuICAgICAgICA8L1RoZW1lUHJvdmlkZXI+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJTZXNzaW9uUHJvdmlkZXIiLCJJMThuUHJvdmlkZXIiLCJUaGVtZVByb3ZpZGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJkaXIiLCJzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmciLCJib2R5IiwiY2xhc3NOYW1lIiwidmFyaWFibGUiLCJhdHRyaWJ1dGUiLCJkZWZhdWx0VGhlbWUiLCJlbmFibGVTeXN0ZW0iLCJkaXNhYmxlVHJhbnNpdGlvbk9uQ2hhbmdlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n\nfunction HomePage() {\n    (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.redirect)(\"/auth/login\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7QUFDMEM7QUFFM0IsU0FBU0M7SUFDdEJELHlEQUFRQSxDQUFDO0FBQ1giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcHAvLi9hcHAvcGFnZS50c3g/NzYwMyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmltcG9ydCB7IHJlZGlyZWN0IH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lUGFnZSgpIHtcbiAgcmVkaXJlY3QoJy9hdXRoL2xvZ2luJylcbn1cbiJdLCJuYW1lcyI6WyJyZWRpcmVjdCIsIkhvbWVQYWdlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\VCode projects\zakat-deepagent\zakat_management_system\app\components\theme-provider.tsx#ThemeProvider`);


/***/ }),

/***/ "(rsc)/./providers/i18n-provider.tsx":
/*!*************************************!*\
  !*** ./providers/i18n-provider.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   I18nProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\VCode projects\zakat-deepagent\zakat_management_system\app\providers\i18n-provider.tsx#I18nProvider`);


/***/ }),

/***/ "(rsc)/./providers/session-provider.tsx":
/*!****************************************!*\
  !*** ./providers/session-provider.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SessionProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\VCode projects\zakat-deepagent\zakat_management_system\app\providers\session-provider.tsx#SessionProvider`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/i18next","vendor-chunks/react-i18next","vendor-chunks/i18next-browser-languagedetector","vendor-chunks/next-themes","vendor-chunks/@swc","vendor-chunks/html-parse-stringify","vendor-chunks/void-elements"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();