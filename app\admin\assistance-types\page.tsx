'use client'

import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { AdminLayout } from '@/components/admin/admin-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Eye, 
  MoreHorizontal,
  Power,
  PowerOff
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import Link from 'next/link'
import { mockAssistanceTypes } from '@/lib/mock-data'

export default function AssistanceTypesPage() {
  const { t, i18n } = useTranslation()
  const [searchTerm, setSearchTerm] = useState('')
  const [assistanceTypes, setAssistanceTypes] = useState(mockAssistanceTypes)

  const filteredTypes = assistanceTypes.filter(type => {
    const searchLower = searchTerm.toLowerCase()
    const nameMatch = (i18n.language === 'ar' ? type.nameAr : type.nameEn)
      .toLowerCase().includes(searchLower)
    const descMatch = (i18n.language === 'ar' ? type.descriptionAr : type.descriptionEn)
      .toLowerCase().includes(searchLower)
    return nameMatch || descMatch
  })

  const handleToggleActive = (id: string) => {
    setAssistanceTypes(prev => 
      prev.map(type => 
        type.id === id ? { ...type, isActive: !type.isActive } : type
      )
    )
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(i18n.language === 'ar' ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR',
      maximumFractionDigits: 0,
    }).format(amount)
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {t('assistance_types_management')}
            </h1>
            <p className="text-muted-foreground">
              {t('assistance_types_management_desc')}
            </p>
          </div>
          <Button asChild>
            <Link href="/admin/assistance-types/new">
              <Plus className="mr-2 h-4 w-4" />
              {t('add_new_type')}
            </Link>
          </Button>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col gap-4 md:flex-row md:items-center">
              <div className="relative flex-1">
                <Search className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search assistance types..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                Filter
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Statistics Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Types</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{assistanceTypes.length}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Types</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {assistanceTypes.filter(t => t.isActive).length}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Inactive Types</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {assistanceTypes.filter(t => !t.isActive).length}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Max Amount</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(Math.max(...assistanceTypes.map(t => t.maxAmount)))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Assistance Types Table */}
        <Card>
          <CardHeader>
            <CardTitle>Assistance Types</CardTitle>
            <CardDescription>
              Showing {filteredTypes.length} of {assistanceTypes.length} assistance types
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Max Amount</TableHead>
                    <TableHead>Required Documents</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-center">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTypes.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <div className="text-muted-foreground">
                          No assistance types found
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredTypes.map((type) => (
                      <TableRow key={type.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {i18n.language === 'ar' ? type.nameAr : type.nameEn}
                            </div>
                            <div className="text-sm text-muted-foreground line-clamp-1">
                              {i18n.language === 'ar' ? type.descriptionAr : type.descriptionEn}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{type.category}</Badge>
                        </TableCell>
                        <TableCell className="font-mono">
                          {formatCurrency(type.maxAmount)}
                        </TableCell>
                        <TableCell>
                          <Badge variant="secondary">
                            {type.requiredDocuments.length} documents
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge 
                            variant={type.isActive ? "default" : "secondary"}
                            className={type.isActive ? "bg-green-100 text-green-800" : ""}
                          >
                            {type.isActive ? 'Active' : 'Inactive'}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-center">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem asChild>
                                <Link href={`/admin/assistance-types/${type.id}`}>
                                  <Eye className="mr-2 h-4 w-4" />
                                  View Details
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem asChild>
                                <Link href={`/admin/assistance-types/${type.id}/edit`}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem 
                                onClick={() => handleToggleActive(type.id)}
                                className={type.isActive ? "text-red-600" : "text-green-600"}
                              >
                                {type.isActive ? (
                                  <>
                                    <PowerOff className="mr-2 h-4 w-4" />
                                    Deactivate
                                  </>
                                ) : (
                                  <>
                                    <Power className="mr-2 h-4 w-4" />
                                    Activate
                                  </>
                                )}
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
