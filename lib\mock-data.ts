
import {
  User,
  PersonalProfile,
  AssistanceRequest,
  AssistanceType,
  Task,
  DashboardStats,
  UserRole,
  Beneficiary,
  ZakatCategory,
  BeneficiaryStatus,
  CaseHistory,
  DistributionRecord,
  FamilyMember,
  BeneficiaryDocument
} from './types';

// Mock Users for each role
export const mockUsers: User[] = [
  // Test admin account
  {
    id: '1',
    nationalId: '**********',
    email: '<EMAIL>',
    fullName: '<PERSON>',
    nationality: 'Saudi Arabia',
    dateOfBirth: new Date('1980-01-01'),
    phoneNumber: '+************',
    address: 'Riyadh, Saudi Arabia',
    accountStatus: 'active',
    role: 'system_admin',
    createdAt: new Date('2023-01-01'),
    lastLogin: new Date(),
  },
  // Zakat Applicants
  {
    id: '2',
    nationalId: '**********',
    email: '<EMAIL>',
    fullName: '<PERSON>',
    nationality: 'Saudi Arabia',
    dateOfBirth: new Date('1985-05-15'),
    phoneNumber: '+************',
    address: 'Jeddah, Saudi Arabia',
    accountStatus: 'active',
    role: 'zakat_applicant',
    createdAt: new Date('2023-02-01'),
    lastLogin: new Date(),
    profileId: 'profile-2',
  },
  {
    id: '3',
    nationalId: '**********',
    email: '<EMAIL>',
    fullName: 'Fatima Omar Al-Zahra',
    nationality: 'Saudi Arabia',
    dateOfBirth: new Date('1990-08-20'),
    phoneNumber: '+************',
    address: 'Dammam, Saudi Arabia',
    accountStatus: 'pending_approval',
    role: 'zakat_applicant',
    createdAt: new Date('2023-03-01'),
    profileId: 'profile-3',
  },
  // Staff Members
  {
    id: '4',
    nationalId: '**********',
    email: '<EMAIL>',
    fullName: 'Sara Abdullah Al-Mansouri',
    nationality: 'Saudi Arabia',
    dateOfBirth: new Date('1988-03-10'),
    phoneNumber: '+************',
    address: 'Riyadh, Saudi Arabia',
    accountStatus: 'active',
    role: 'reception_staff',
    createdAt: new Date('2022-01-15'),
    lastLogin: new Date(),
  },
  {
    id: '5',
    nationalId: '**********',
    email: '<EMAIL>',
    fullName: 'Mohammed Hassan Al-Qadiri',
    nationality: 'Saudi Arabia',
    dateOfBirth: new Date('1982-07-25'),
    phoneNumber: '+************',
    address: 'Riyadh, Saudi Arabia',
    accountStatus: 'active',
    role: 'researcher',
    createdAt: new Date('2022-02-01'),
    lastLogin: new Date(),
  },
  {
    id: '6',
    nationalId: '**********',
    email: '<EMAIL>',
    fullName: 'Khalid Ahmed Al-Othman',
    nationality: 'Saudi Arabia',
    dateOfBirth: new Date('1975-12-05'),
    phoneNumber: '+************',
    address: 'Riyadh, Saudi Arabia',
    accountStatus: 'active',
    role: 'banking_expert',
    createdAt: new Date('2021-01-01'),
    lastLogin: new Date(),
  },
  {
    id: '7',
    nationalId: '**********',
    email: '<EMAIL>',
    fullName: 'Nasser Fahad Al-Saud',
    nationality: 'Saudi Arabia',
    dateOfBirth: new Date('1970-04-18'),
    phoneNumber: '+************',
    address: 'Riyadh, Saudi Arabia',
    accountStatus: 'active',
    role: 'department_head',
    createdAt: new Date('2020-01-01'),
    lastLogin: new Date(),
  },
  {
    id: '8',
    nationalId: '**********',
    email: '<EMAIL>',
    fullName: 'Abdulaziz Mohammed Al-Rashid',
    nationality: 'Saudi Arabia',
    dateOfBirth: new Date('1968-09-12'),
    phoneNumber: '+************',
    address: 'Riyadh, Saudi Arabia',
    accountStatus: 'active',
    role: 'admin_manager',
    createdAt: new Date('2019-01-01'),
    lastLogin: new Date(),
  },
  {
    id: '9',
    nationalId: '**********',
    email: '<EMAIL>',
    fullName: 'His Excellency Abdullah bin Salman',
    nationality: 'Saudi Arabia',
    dateOfBirth: new Date('1965-02-28'),
    phoneNumber: '+************',
    address: 'Riyadh, Saudi Arabia',
    accountStatus: 'active',
    role: 'minister',
    createdAt: new Date('2018-01-01'),
    lastLogin: new Date(),
  },
];

// Mock Document Types for Admin
export const mockDocumentTypes = [
  {
    id: 'salary-certificate',
    nameAr: 'شهادة راتب',
    nameEn: 'Salary Certificate',
    descriptionAr: 'شهادة تثبت الراتب الشهري للموظف',
    descriptionEn: 'Certificate proving employee monthly salary',
    acceptedFormats: ['pdf', 'jpg', 'png'],
    maxSizeKB: 2048,
    isActive: true,
    usageCount: 45
  },
  {
    id: 'bank-statement',
    nameAr: 'كشف حساب بنكي',
    nameEn: 'Bank Statement',
    descriptionAr: 'كشف حساب بنكي للأشهر الثلاثة الماضية',
    descriptionEn: 'Bank statement for the last three months',
    acceptedFormats: ['pdf'],
    maxSizeKB: 5120,
    isActive: true,
    usageCount: 38
  },
  {
    id: 'medical-report',
    nameAr: 'تقرير طبي',
    nameEn: 'Medical Report',
    descriptionAr: 'تقرير طبي معتمد من مستشفى أو عيادة',
    descriptionEn: 'Certified medical report from hospital or clinic',
    acceptedFormats: ['pdf', 'jpg', 'png'],
    maxSizeKB: 3072,
    isActive: true,
    usageCount: 22
  }
]

// Mock Assistance Types
export const mockAssistanceTypes: AssistanceType[] = [
  {
    id: 'financial-support',
    nameAr: 'المساعدة المالية العامة',
    nameEn: 'General Financial Support',
    descriptionAr: 'مساعدة مالية للأسر المحتاجة',
    descriptionEn: 'Financial assistance for needy families',
    maxAmount: 25000,
    requiredDocuments: [
      {
        id: 'salary-certificate',
        nameAr: 'شهادة راتب',
        nameEn: 'Salary Certificate',
        isRequired: true,
        acceptedFormats: ['pdf', 'jpg', 'png'],
        maxSizeKB: 2048,
      },
      {
        id: 'bank-statement',
        nameAr: 'كشف حساب بنكي',
        nameEn: 'Bank Statement',
        isRequired: true,
        acceptedFormats: ['pdf'],
        maxSizeKB: 5120,
      },
    ],
    eligibilityCriteria: [
      {
        field: 'monthlyIncome',
        condition: 'less_than',
        value: 5000,
        nationality: 'Saudi Arabia',
      },
    ],
    isActive: true,
    category: 'Financial',
  },
  {
    id: 'medical-support',
    nameAr: 'المساعدة الطبية',
    nameEn: 'Medical Support',
    descriptionAr: 'مساعدة لتغطية التكاليف الطبية',
    descriptionEn: 'Assistance to cover medical expenses',
    maxAmount: 50000,
    requiredDocuments: [
      {
        id: 'medical-report',
        nameAr: 'التقرير الطبي',
        nameEn: 'Medical Report',
        isRequired: true,
        acceptedFormats: ['pdf', 'jpg', 'png'],
        maxSizeKB: 3072,
      },
      {
        id: 'medical-bills',
        nameAr: 'الفواتير الطبية',
        nameEn: 'Medical Bills',
        isRequired: true,
        acceptedFormats: ['pdf', 'jpg', 'png'],
        maxSizeKB: 5120,
      },
    ],
    eligibilityCriteria: [],
    isActive: true,
    category: 'Medical',
  },
  {
    id: 'education-support',
    nameAr: 'المساعدة التعليمية',
    nameEn: 'Education Support',
    descriptionAr: 'مساعدة لتغطية تكاليف التعليم',
    descriptionEn: 'Assistance to cover education expenses',
    maxAmount: 15000,
    requiredDocuments: [
      {
        id: 'enrollment-certificate',
        nameAr: 'شهادة قيد',
        nameEn: 'Enrollment Certificate',
        isRequired: true,
        acceptedFormats: ['pdf', 'jpg', 'png'],
        maxSizeKB: 2048,
      },
    ],
    eligibilityCriteria: [
      {
        field: 'familyMembersCount',
        condition: 'greater_than',
        value: 0,
      },
    ],
    isActive: true,
    category: 'Education',
  },
];

// Mock Assistance Requests
export const mockAssistanceRequests: AssistanceRequest[] = [
  {
    id: 'req-001',
    userId: '2',
    assistanceType: mockAssistanceTypes[0],
    requestedAmount: 15000,
    approvedAmount: 12000,
    description: 'نحتاج إلى مساعدة مالية لتغطية تكاليف المعيشة بعد توقف العمل',
    status: 'approved',
    submissionDate: new Date('2023-10-15'),
    lastUpdateDate: new Date('2023-11-01'),
    attachedDocuments: [],
    workflow: [
      {
        id: 'step-1',
        requestId: 'req-001',
        stage: 'reception_review',
        reviewerId: '4',
        reviewerName: 'Sara Abdullah Al-Mansouri',
        stageStartDate: new Date('2023-10-15'),
        stageEndDate: new Date('2023-10-16'),
        decision: 'approve',
        decisionDate: new Date('2023-10-16'),
        notes: 'المستندات كاملة والحالة تستدعي المساعدة',
      },
      {
        id: 'step-2',
        requestId: 'req-001',
        stage: 'approved',
        reviewerId: '8',
        reviewerName: 'Abdulaziz Mohammed Al-Rashid',
        stageStartDate: new Date('2023-10-30'),
        stageEndDate: new Date('2023-11-01'),
        decision: 'approve',
        decisionDate: new Date('2023-11-01'),
        notes: 'تمت الموافقة على مبلغ 12,000 ريال',
      },
    ],
    priority: 'medium',
  },
  {
    id: 'req-002',
    userId: '3',
    assistanceType: mockAssistanceTypes[1],
    requestedAmount: 35000,
    description: 'نحتاج مساعدة لتغطية تكاليف علاج والدي في المستشفى',
    status: 'researcher_review',
    submissionDate: new Date('2023-11-10'),
    lastUpdateDate: new Date('2023-11-12'),
    attachedDocuments: [],
    workflow: [
      {
        id: 'step-1',
        requestId: 'req-002',
        stage: 'reception_review',
        reviewerId: '4',
        reviewerName: 'Sara Abdullah Al-Mansouri',
        stageStartDate: new Date('2023-11-10'),
        stageEndDate: new Date('2023-11-11'),
        decision: 'approve',
        decisionDate: new Date('2023-11-11'),
        notes: 'تم مراجعة الطلب وإحالته للباحث',
      },
      {
        id: 'step-2',
        requestId: 'req-002',
        stage: 'researcher_review',
        reviewerId: '5',
        reviewerName: 'Mohammed Hassan Al-Qadiri',
        stageStartDate: new Date('2023-11-11'),
        notes: 'قيد المراجعة والتحقق من المستندات الطبية',
      },
    ],
    priority: 'high',
  },
];

// Mock Tasks for different roles
export const mockTasks: Record<UserRole, Task[]> = {
  reception_staff: [
    {
      id: 'task-1',
      assignedTo: '4',
      requestId: 'req-003',
      type: 'profile_review',
      priority: 'medium',
      dueDate: new Date(Date.now() + ********), // Tomorrow
      status: 'pending',
      createdDate: new Date(),
    },
    {
      id: 'task-2',
      assignedTo: '4',
      requestId: 'req-004',
      type: 'request_review',
      priority: 'high',
      status: 'in_progress',
      createdDate: new Date(Date.now() - ********), // Yesterday
    },
  ],
  researcher: [
    {
      id: 'task-3',
      assignedTo: '5',
      requestId: 'req-002',
      type: 'request_review',
      priority: 'high',
      dueDate: new Date(Date.now() + *********), // Day after tomorrow
      status: 'in_progress',
      createdDate: new Date('2023-11-11'),
    },
  ],
  banking_expert: [
    {
      id: 'task-4',
      assignedTo: '6',
      requestId: 'req-005',
      type: 'request_review',
      priority: 'medium',
      dueDate: new Date(Date.now() + *********), // 3 days from now
      status: 'pending',
      createdDate: new Date(),
    },
  ],
  department_head: [],
  admin_manager: [],
  minister: [],
  zakat_applicant: [],
  system_admin: [],
};

// Mock Dashboard Stats for different roles
export const mockDashboardStats: Record<UserRole, DashboardStats> = {
  reception_staff: {
    totalRequests: 45,
    pendingReview: 8,
    approvedToday: 3,
    rejectedToday: 1,
    averageProcessingDays: 2,
    totalUsers: 150,
  },
  researcher: {
    totalRequests: 32,
    pendingReview: 5,
    approvedToday: 2,
    rejectedToday: 0,
    averageProcessingDays: 3,
    totalUsers: 150,
  },
  banking_expert: {
    totalRequests: 28,
    pendingReview: 4,
    approvedToday: 1,
    rejectedToday: 1,
    averageProcessingDays: 4,
    totalUsers: 150,
  },
  department_head: {
    totalRequests: 15,
    pendingReview: 2,
    approvedToday: 1,
    rejectedToday: 0,
    averageProcessingDays: 5,
    totalUsers: 150,
  },
  admin_manager: {
    totalRequests: 120,
    pendingReview: 8,
    approvedToday: 5,
    rejectedToday: 2,
    averageProcessingDays: 12,
    totalUsers: 150,
  },
  minister: {
    totalRequests: 8,
    pendingReview: 1,
    approvedToday: 0,
    rejectedToday: 0,
    averageProcessingDays: 7,
    totalUsers: 150,
  },
  zakat_applicant: {
    totalRequests: 3,
    pendingReview: 1,
    approvedToday: 0,
    rejectedToday: 0,
    averageProcessingDays: 14,
    totalUsers: 1,
  },
  system_admin: {
    totalRequests: 200,
    pendingReview: 25,
    approvedToday: 12,
    rejectedToday: 3,
    averageProcessingDays: 10,
    totalUsers: 150,
  },
};

// Helper function to get user by ID
export const getUserById = (id: string): User | undefined => {
  return mockUsers.find(user => user.id === id);
};

// Helper function to get user by email
export const getUserByEmail = (email: string): User | undefined => {
  return mockUsers.find(user => user.email === email);
};

// Helper function to get requests by user ID
export const getRequestsByUserId = (userId: string): AssistanceRequest[] => {
  return mockAssistanceRequests.filter(request => request.userId === userId);
};

// Helper function to get tasks by user ID
export const getTasksByUserId = (userId: string): Task[] => {
  const user = getUserById(userId);
  if (!user) return [];
  return mockTasks[user.role] || [];
};

// Zakat Category Labels
export const zakatCategoryLabels: Record<ZakatCategory, { ar: string; en: string }> = {
  fuqara: { ar: 'الفقراء', en: 'The Poor' },
  masakin: { ar: 'المساكين', en: 'The Needy' },
  amilin: { ar: 'العاملين عليها', en: 'Zakat Administrators' },
  muallafah: { ar: 'المؤلفة قلوبهم', en: 'Those whose hearts are reconciled' },
  riqab: { ar: 'في الرقاب', en: 'To free slaves/captives' },
  gharimin: { ar: 'الغارمين', en: 'Those in debt' },
  fisabilillah: { ar: 'في سبيل الله', en: 'In the cause of Allah' },
  ibnus_sabil: { ar: 'ابن السبيل', en: 'The wayfarer/traveler' }
};

// Mock Beneficiaries Data
export const mockBeneficiaries: Beneficiary[] = [
  {
    id: 'ben-001',
    fullNameAr: 'أحمد محمد العبدالله',
    fullNameEn: 'Ahmed Mohammed Al-Abdullah',
    nationalId: '**********',
    dateOfBirth: new Date('1985-03-15'),
    gender: 'male',
    maritalStatus: 'married',
    phoneNumber: '+966501234567',
    email: '<EMAIL>',
    address: 'حي النهضة، شارع الملك فهد',
    city: 'الرياض',
    region: 'منطقة الرياض',
    postalCode: '12345',
    zakatCategories: ['fuqara', 'gharimin'],
    primaryCategory: 'fuqara',
    eligibilityScore: 85,
    monthlyIncome: 2500,
    familySize: 5,
    dependents: 3,
    status: 'approved',
    verificationStatus: 'completed',
    registrationDate: new Date('2024-01-15'),
    lastVerificationDate: new Date('2024-01-20'),
    nextReviewDate: new Date('2024-07-15'),
    caseId: 'case-001',
    assignedStaffId: '4',
    priority: 'medium',
    totalReceived: 15000,
    lastDistributionDate: new Date('2024-01-25'),
    distributionCount: 3,
    documents: [
      {
        id: 'doc-001',
        type: 'national_id',
        name: 'National ID Copy',
        uploadDate: new Date('2024-01-15'),
        verified: true,
        verifiedBy: '4',
        verifiedAt: new Date('2024-01-16'),
        fileSize: 1024000,
        mimeType: 'image/jpeg'
      }
    ],
    createdBy: '4',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-25'),
    notes: 'Family breadwinner, needs regular support'
  },
  {
    id: 'ben-002',
    fullNameAr: 'فاطمة علي الزهراء',
    fullNameEn: 'Fatima Ali Al-Zahra',
    nationalId: '**********',
    dateOfBirth: new Date('1990-07-22'),
    gender: 'female',
    maritalStatus: 'widowed',
    phoneNumber: '+966502345678',
    email: '<EMAIL>',
    address: 'حي الملز، شارع العليا',
    city: 'الرياض',
    region: 'منطقة الرياض',
    zakatCategories: ['masakin', 'fuqara'],
    primaryCategory: 'masakin',
    eligibilityScore: 92,
    monthlyIncome: 1200,
    familySize: 4,
    dependents: 3,
    status: 'approved',
    verificationStatus: 'completed',
    registrationDate: new Date('2024-02-01'),
    lastVerificationDate: new Date('2024-02-05'),
    nextReviewDate: new Date('2024-08-01'),
    caseId: 'case-002',
    assignedStaffId: '5',
    priority: 'high',
    totalReceived: 22000,
    lastDistributionDate: new Date('2024-02-10'),
    distributionCount: 4,
    familyMembers: [
      {
        id: 'fam-001',
        name: 'محمد علي الزهراء',
        relationship: 'son',
        age: 12,
        isDependent: true,
        hasSpecialNeeds: false,
        hasDisability: false
      },
      {
        id: 'fam-002',
        name: 'عائشة علي الزهراء',
        relationship: 'daughter',
        age: 8,
        isDependent: true,
        hasSpecialNeeds: true,
        hasDisability: true
      }
    ],
    documents: [
      {
        id: 'doc-002',
        type: 'family_card',
        name: 'Family Registration Card',
        uploadDate: new Date('2024-02-01'),
        verified: true,
        verifiedBy: '5',
        verifiedAt: new Date('2024-02-02'),
        fileSize: 2048000,
        mimeType: 'application/pdf'
      }
    ],
    createdBy: '5',
    createdAt: new Date('2024-02-01'),
    updatedAt: new Date('2024-02-10'),
    notes: 'Widow with special needs child, priority case'
  },
  {
    id: 'ben-003',
    fullNameAr: 'خالد سعد الغامدي',
    fullNameEn: 'Khalid Saad Al-Ghamdi',
    nationalId: '**********',
    dateOfBirth: new Date('1978-11-10'),
    gender: 'male',
    maritalStatus: 'married',
    phoneNumber: '+966503456789',
    address: 'حي الشفا، طريق الملك عبدالعزيز',
    city: 'جدة',
    region: 'منطقة مكة المكرمة',
    zakatCategories: ['gharimin', 'fuqara'],
    primaryCategory: 'gharimin',
    eligibilityScore: 78,
    monthlyIncome: 3200,
    familySize: 6,
    dependents: 4,
    status: 'under_review',
    verificationStatus: 'in_progress',
    registrationDate: new Date('2024-02-15'),
    caseId: 'case-003',
    assignedStaffId: '6',
    priority: 'medium',
    totalReceived: 8000,
    lastDistributionDate: new Date('2024-01-10'),
    distributionCount: 2,
    documents: [
      {
        id: 'doc-003',
        type: 'income_certificate',
        name: 'Income Certificate',
        uploadDate: new Date('2024-02-15'),
        verified: false,
        fileSize: 1536000,
        mimeType: 'application/pdf'
      }
    ],
    createdBy: '6',
    createdAt: new Date('2024-02-15'),
    updatedAt: new Date('2024-02-16'),
    notes: 'Business owner facing financial difficulties'
  },
  {
    id: 'ben-004',
    fullNameAr: 'مريم عبدالرحمن القحطاني',
    fullNameEn: 'Maryam Abdulrahman Al-Qahtani',
    nationalId: '**********',
    dateOfBirth: new Date('1995-04-18'),
    gender: 'female',
    maritalStatus: 'single',
    phoneNumber: '+966504567890',
    address: 'حي الروضة، شارع التحلية',
    city: 'الدمام',
    region: 'المنطقة الشرقية',
    zakatCategories: ['ibnus_sabil'],
    primaryCategory: 'ibnus_sabil',
    eligibilityScore: 65,
    monthlyIncome: 0,
    familySize: 1,
    dependents: 0,
    status: 'pending_verification',
    verificationStatus: 'pending',
    registrationDate: new Date('2024-02-20'),
    priority: 'low',
    totalReceived: 0,
    distributionCount: 0,
    documents: [
      {
        id: 'doc-004',
        type: 'national_id',
        name: 'National ID Copy',
        uploadDate: new Date('2024-02-20'),
        verified: false,
        fileSize: 896000,
        mimeType: 'image/png'
      }
    ],
    createdBy: '4',
    createdAt: new Date('2024-02-20'),
    updatedAt: new Date('2024-02-20'),
    notes: 'Student seeking temporary assistance'
  },
  {
    id: 'ben-005',
    fullNameAr: 'عبدالله يوسف الشهري',
    fullNameEn: 'Abdullah Yusuf Al-Shehri',
    nationalId: '**********',
    dateOfBirth: new Date('1982-09-05'),
    gender: 'male',
    maritalStatus: 'divorced',
    phoneNumber: '+966505678901',
    address: 'حي العزيزية، شارع الأمير سلطان',
    city: 'الطائف',
    region: 'منطقة مكة المكرمة',
    zakatCategories: ['fuqara', 'gharimin'],
    primaryCategory: 'fuqara',
    eligibilityScore: 88,
    monthlyIncome: 1800,
    familySize: 3,
    dependents: 2,
    status: 'approved',
    verificationStatus: 'completed',
    registrationDate: new Date('2024-01-05'),
    lastVerificationDate: new Date('2024-01-10'),
    nextReviewDate: new Date('2024-07-05'),
    caseId: 'case-005',
    assignedStaffId: '4',
    priority: 'medium',
    totalReceived: 18500,
    lastDistributionDate: new Date('2024-02-01'),
    distributionCount: 5,
    familyMembers: [
      {
        id: 'fam-003',
        name: 'سارة عبدالله الشهري',
        relationship: 'daughter',
        age: 10,
        isDependent: true,
        hasSpecialNeeds: false,
        hasDisability: false
      },
      {
        id: 'fam-004',
        name: 'عمر عبدالله الشهري',
        relationship: 'son',
        age: 7,
        isDependent: true,
        hasSpecialNeeds: false,
        hasDisability: false
      }
    ],
    documents: [
      {
        id: 'doc-005',
        type: 'family_card',
        name: 'Family Registration Card',
        uploadDate: new Date('2024-01-05'),
        verified: true,
        verifiedBy: '4',
        verifiedAt: new Date('2024-01-06'),
        fileSize: 1792000,
        mimeType: 'application/pdf'
      }
    ],
    createdBy: '4',
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-02-01'),
    notes: 'Divorced father with custody of children'
  },
  {
    id: 'ben-006',
    fullNameAr: 'نورا أحمد الحربي',
    fullNameEn: 'Nora Ahmed Al-Harbi',
    nationalId: '**********',
    dateOfBirth: new Date('1988-12-03'),
    gender: 'female',
    maritalStatus: 'married',
    phoneNumber: '+966506789012',
    email: '<EMAIL>',
    address: 'حي الملقا، شارع الأمير محمد بن عبدالعزيز',
    city: 'الرياض',
    region: 'منطقة الرياض',
    zakatCategories: ['masakin', 'fuqara'],
    primaryCategory: 'masakin',
    eligibilityScore: 91,
    monthlyIncome: 1500,
    familySize: 7,
    dependents: 5,
    status: 'approved',
    verificationStatus: 'completed',
    registrationDate: new Date('2024-01-20'),
    lastVerificationDate: new Date('2024-01-25'),
    nextReviewDate: new Date('2024-07-20'),
    caseId: 'case-006',
    assignedStaffId: '5',
    priority: 'high',
    totalReceived: 25000,
    lastDistributionDate: new Date('2024-02-15'),
    distributionCount: 6,
    documents: [
      {
        id: 'doc-006',
        type: 'family_card',
        name: 'Family Registration Card',
        uploadDate: new Date('2024-01-20'),
        verified: true,
        verifiedBy: '5',
        verifiedAt: new Date('2024-01-21'),
        fileSize: 2304000,
        mimeType: 'application/pdf'
      }
    ],
    createdBy: '5',
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-02-15'),
    notes: 'Large family with multiple dependents'
  },
  {
    id: 'ben-007',
    fullNameAr: 'محمد عبدالله الدوسري',
    fullNameEn: 'Mohammed Abdullah Al-Dosari',
    nationalId: '**********',
    dateOfBirth: new Date('1975-06-14'),
    gender: 'male',
    maritalStatus: 'married',
    phoneNumber: '+966507890123',
    address: 'حي الفيصلية، طريق الملك فهد',
    city: 'الدمام',
    region: 'المنطقة الشرقية',
    zakatCategories: ['gharimin', 'fuqara'],
    primaryCategory: 'gharimin',
    eligibilityScore: 82,
    monthlyIncome: 2800,
    familySize: 4,
    dependents: 2,
    status: 'approved',
    verificationStatus: 'completed',
    registrationDate: new Date('2023-12-10'),
    lastVerificationDate: new Date('2023-12-15'),
    nextReviewDate: new Date('2024-06-10'),
    caseId: 'case-007',
    assignedStaffId: '6',
    priority: 'medium',
    totalReceived: 12000,
    lastDistributionDate: new Date('2024-01-30'),
    distributionCount: 4,
    documents: [
      {
        id: 'doc-007',
        type: 'income_certificate',
        name: 'Income Certificate',
        uploadDate: new Date('2023-12-10'),
        verified: true,
        verifiedBy: '6',
        verifiedAt: new Date('2023-12-11'),
        fileSize: 1280000,
        mimeType: 'application/pdf'
      }
    ],
    createdBy: '6',
    createdAt: new Date('2023-12-10'),
    updatedAt: new Date('2024-01-30'),
    notes: 'Small business owner with debt issues'
  },
  {
    id: 'ben-008',
    fullNameAr: 'عائشة سالم القرني',
    fullNameEn: 'Aisha Salem Al-Qarni',
    nationalId: '**********',
    dateOfBirth: new Date('1992-08-27'),
    gender: 'female',
    maritalStatus: 'single',
    phoneNumber: '+966508901234',
    address: 'حي الشرفية، شارع الستين',
    city: 'جدة',
    region: 'منطقة مكة المكرمة',
    zakatCategories: ['fuqara'],
    primaryCategory: 'fuqara',
    eligibilityScore: 75,
    monthlyIncome: 1000,
    familySize: 2,
    dependents: 1,
    status: 'under_review',
    verificationStatus: 'in_progress',
    registrationDate: new Date('2024-02-25'),
    caseId: 'case-008',
    assignedStaffId: '4',
    priority: 'medium',
    totalReceived: 3000,
    lastDistributionDate: new Date('2024-01-15'),
    distributionCount: 1,
    familyMembers: [
      {
        id: 'fam-005',
        name: 'فاطمة سالم القرني',
        relationship: 'mother',
        age: 65,
        isDependent: true,
        hasSpecialNeeds: true,
        hasDisability: true
      }
    ],
    documents: [
      {
        id: 'doc-008',
        type: 'medical_report',
        name: 'Medical Report for Mother',
        uploadDate: new Date('2024-02-25'),
        verified: false,
        fileSize: 3072000,
        mimeType: 'application/pdf'
      }
    ],
    createdBy: '4',
    createdAt: new Date('2024-02-25'),
    updatedAt: new Date('2024-02-26'),
    notes: 'Caring for elderly mother with medical needs'
  }
];

// Helper functions for beneficiaries
export const getBeneficiaryById = (id: string): Beneficiary | undefined => {
  return mockBeneficiaries.find(beneficiary => beneficiary.id === id);
};

export const getBeneficiariesByStatus = (status: BeneficiaryStatus): Beneficiary[] => {
  return mockBeneficiaries.filter(beneficiary => beneficiary.status === status);
};

export const getBeneficiariesByCategory = (category: ZakatCategory): Beneficiary[] => {
  return mockBeneficiaries.filter(beneficiary =>
    beneficiary.zakatCategories.includes(category) || beneficiary.primaryCategory === category
  );
};

export const searchBeneficiaries = (searchTerm: string): Beneficiary[] => {
  const term = searchTerm.toLowerCase();
  return mockBeneficiaries.filter(beneficiary =>
    beneficiary.fullNameAr.toLowerCase().includes(term) ||
    beneficiary.fullNameEn.toLowerCase().includes(term) ||
    beneficiary.nationalId.includes(term) ||
    beneficiary.phoneNumber.includes(term) ||
    beneficiary.email?.toLowerCase().includes(term)
  );
};

export const getBeneficiaryStats = () => {
  const total = mockBeneficiaries.length;
  const approved = mockBeneficiaries.filter(b => b.status === 'approved').length;
  const pending = mockBeneficiaries.filter(b => b.status === 'pending_verification').length;
  const underReview = mockBeneficiaries.filter(b => b.status === 'under_review').length;
  const totalDistributed = mockBeneficiaries.reduce((sum, b) => sum + b.totalReceived, 0);

  return {
    total,
    approved,
    pending,
    underReview,
    totalDistributed,
    averageDistribution: total > 0 ? Math.round(totalDistributed / total) : 0
  };
};
