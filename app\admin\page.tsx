'use client'

import { useSession } from 'next-auth/react'
import { useTranslation } from 'react-i18next'
import { AdminLayout } from '@/components/admin/admin-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Users, 
  CreditCard, 
  Settings, 
  Workflow, 
  FileText, 
  Shield,
  BarChart3,
  Database
} from 'lucide-react'
import Link from 'next/link'

export default function AdminDashboardPage() {
  const { data: session } = useSession() || {}
  const { t } = useTranslation()

  if (!session?.user) {
    return null
  }

  // Only system admins can access this page
  if (session.user.role !== 'system_admin') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">{t('access_denied')}</h2>
          <p className="text-muted-foreground">{t('admin_access_required')}</p>
        </div>
      </div>
    )
  }

  const adminSections = [
    {
      title: 'User Management',
      description: 'Manage user accounts, roles, and permissions',
      icon: Users,
      href: '/admin/users',
      color: 'bg-blue-500'
    },
    {
      title: 'Assistance Types',
      description: 'Configure aid types, eligibility criteria, and requirements',
      icon: CreditCard,
      href: '/admin/assistance-types',
      color: 'bg-green-500'
    },
    {
      title: 'Workflow Management',
      description: 'Configure approval workflows and business rules',
      icon: Workflow,
      href: '/admin/workflow',
      color: 'bg-purple-500'
    },
    {
      title: 'Document Configuration',
      description: 'Manage document types and requirements',
      icon: FileText,
      href: '/admin/documents',
      color: 'bg-orange-500'
    },
    {
      title: 'Distribution Rules',
      description: 'Configure automatic application distribution',
      icon: BarChart3,
      href: '/admin/distribution',
      color: 'bg-indigo-500'
    },
    {
      title: 'System Settings',
      description: 'General system configuration and parameters',
      icon: Settings,
      href: '/admin/settings',
      color: 'bg-gray-500'
    },
    {
      title: 'Audit Trail',
      description: 'View system activity logs and audit trails',
      icon: Shield,
      href: '/admin/audit',
      color: 'bg-red-500'
    },
    {
      title: 'Data Management',
      description: 'Import/export data and backup management',
      icon: Database,
      href: '/admin/data',
      color: 'bg-teal-500'
    }
  ]

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            System Administration
          </h1>
          <p className="text-muted-foreground">
            Manage system configuration, users, and operational parameters
          </p>
        </div>

        {/* Admin Sections Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {adminSections.map((section) => (
            <Card key={section.href} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${section.color} text-white`}>
                    <section.icon className="h-5 w-5" />
                  </div>
                  <CardTitle className="text-lg">{section.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                <CardDescription className="text-sm">
                  {section.description}
                </CardDescription>
                <Button asChild className="w-full">
                  <Link href={section.href}>
                    Manage
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">150</div>
              <p className="text-xs text-muted-foreground">
                +12 from last month
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Aid Types</CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">8</div>
              <p className="text-xs text-muted-foreground">
                3 recently updated
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Workflows</CardTitle>
              <Workflow className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">5</div>
              <p className="text-xs text-muted-foreground">
                All operational
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">System Health</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">Good</div>
              <p className="text-xs text-muted-foreground">
                All systems operational
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
  )
}
