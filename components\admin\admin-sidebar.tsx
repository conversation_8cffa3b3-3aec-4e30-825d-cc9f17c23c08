'use client'

import { usePathname } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import Link from 'next/link'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { 
  LayoutDashboard,
  Users, 
  CreditCard, 
  Settings, 
  Workflow, 
  FileText, 
  Shield,
  BarChart3,
  Database,
  ArrowLeft,
  Home
} from 'lucide-react'

interface AdminSidebarItem {
  title: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  description?: string
}

const adminSidebarItems: AdminSidebarItem[] = [
  {
    title: 'Admin Dashboard',
    href: '/admin',
    icon: LayoutDashboard,
    description: 'System administration overview'
  },
  {
    title: 'User Management',
    href: '/admin/users',
    icon: Users,
    description: 'Manage user accounts and roles'
  },
  {
    title: 'Assistance Types',
    href: '/admin/assistance-types',
    icon: CreditCard,
    description: 'Configure aid types and eligibility'
  },
  {
    title: 'Workflow Management',
    href: '/admin/workflow',
    icon: Workflow,
    description: 'Configure approval workflows'
  },
  {
    title: 'Document Configuration',
    href: '/admin/documents',
    icon: FileText,
    description: 'Manage document requirements'
  },
  {
    title: 'Distribution Rules',
    href: '/admin/distribution',
    icon: BarChart3,
    description: 'Configure application distribution'
  },
  {
    title: 'System Settings',
    href: '/admin/settings',
    icon: Settings,
    description: 'General system configuration'
  },
  {
    title: 'Audit Trail',
    href: '/admin/audit',
    icon: Shield,
    description: 'View system activity logs'
  },
  {
    title: 'Data Management',
    href: '/admin/data',
    icon: Database,
    description: 'Import/export and backup'
  }
]

export function AdminSidebar() {
  const pathname = usePathname()
  const { t } = useTranslation()

  return (
    <div className="w-64 border-r bg-muted/10 flex flex-col">
      {/* Header */}
      <div className="p-6 border-b">
        <div className="flex items-center gap-2 mb-4">
          <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
            <span className="text-primary-foreground font-bold text-sm">ز</span>
          </div>
          <div>
            <h2 className="font-semibold text-sm">Zakat System</h2>
            <p className="text-xs text-muted-foreground">Administration</p>
          </div>
        </div>
        
        {/* Back to Main System */}
        <Button variant="outline" size="sm" asChild className="w-full">
          <Link href="/dashboard">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Link>
        </Button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {adminSidebarItems.map((item) => {
          const isActive = pathname === item.href
          
          return (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "flex items-center gap-3 px-3 py-2 rounded-lg text-sm transition-colors",
                "hover:bg-accent hover:text-accent-foreground",
                isActive 
                  ? "bg-accent text-accent-foreground font-medium" 
                  : "text-muted-foreground"
              )}
            >
              <item.icon className="h-4 w-4" />
              <div className="flex-1">
                <div className="font-medium">{item.title}</div>
                {item.description && (
                  <div className="text-xs text-muted-foreground mt-0.5">
                    {item.description}
                  </div>
                )}
              </div>
            </Link>
          )
        })}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t">
        <div className="text-xs text-muted-foreground text-center">
          System Administration
          <br />
          Version 1.0.0
        </div>
      </div>
    </div>
  )
}
