'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useTranslation } from 'react-i18next'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { 
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Plus, X, FileText } from 'lucide-react'
import type { AssistanceType, RequiredDocument } from '@/lib/types'

const assistanceTypeSchema = z.object({
  nameAr: z.string().min(1, 'Arabic name is required'),
  nameEn: z.string().min(1, 'English name is required'),
  descriptionAr: z.string().min(1, 'Arabic description is required'),
  descriptionEn: z.string().min(1, 'English description is required'),
  maxAmount: z.number().min(1, 'Maximum amount must be greater than 0'),
  category: z.string().min(1, 'Category is required'),
  isActive: z.boolean().default(true),
})

type AssistanceTypeFormData = z.infer<typeof assistanceTypeSchema>

interface AssistanceTypeFormProps {
  initialData?: Partial<AssistanceType>
  onSubmit: (data: AssistanceTypeFormData & { requiredDocuments: RequiredDocument[] }) => void
  onCancel: () => void
  isLoading?: boolean
}

const categories = [
  'Financial Support',
  'Medical Assistance',
  'Educational Aid',
  'Housing Support',
  'Emergency Relief',
  'Disability Support',
  'Elderly Care',
  'Family Support'
]

export function AssistanceTypeForm({ 
  initialData, 
  onSubmit, 
  onCancel, 
  isLoading = false 
}: AssistanceTypeFormProps) {
  const { t } = useTranslation()
  const [requiredDocuments, setRequiredDocuments] = useState<RequiredDocument[]>(
    initialData?.requiredDocuments || []
  )

  const form = useForm<AssistanceTypeFormData>({
    resolver: zodResolver(assistanceTypeSchema),
    defaultValues: {
      nameAr: initialData?.nameAr || '',
      nameEn: initialData?.nameEn || '',
      descriptionAr: initialData?.descriptionAr || '',
      descriptionEn: initialData?.descriptionEn || '',
      maxAmount: initialData?.maxAmount || 0,
      category: initialData?.category || '',
      isActive: initialData?.isActive ?? true,
    },
  })

  const handleSubmit = (data: AssistanceTypeFormData) => {
    onSubmit({ ...data, requiredDocuments })
  }

  const addDocument = () => {
    const newDoc: RequiredDocument = {
      id: `doc-${Date.now()}`,
      nameAr: '',
      nameEn: '',
      isRequired: true,
      acceptedFormats: ['pdf'],
      maxSizeKB: 2048,
    }
    setRequiredDocuments([...requiredDocuments, newDoc])
  }

  const updateDocument = (index: number, field: keyof RequiredDocument, value: any) => {
    const updated = [...requiredDocuments]
    updated[index] = { ...updated[index], [field]: value }
    setRequiredDocuments(updated)
  }

  const removeDocument = (index: number) => {
    setRequiredDocuments(requiredDocuments.filter((_, i) => i !== index))
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <CardDescription>
              Configure the basic details of the assistance type
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="nameAr"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name (Arabic)</FormLabel>
                    <FormControl>
                      <Input placeholder="اسم نوع المساعدة" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="nameEn"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name (English)</FormLabel>
                    <FormControl>
                      <Input placeholder="Assistance type name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="descriptionAr"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description (Arabic)</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="وصف نوع المساعدة" 
                        className="min-h-[100px]"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="descriptionEn"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description (English)</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Assistance type description" 
                        className="min-h-[100px]"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid gap-4 md:grid-cols-3">
              <FormField
                control={form.control}
                name="maxAmount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Maximum Amount (SAR)</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        placeholder="25000" 
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Active Status</FormLabel>
                      <FormDescription>
                        Enable this assistance type for applications
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Required Documents */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Required Documents</CardTitle>
                <CardDescription>
                  Configure documents required for this assistance type
                </CardDescription>
              </div>
              <Button type="button" variant="outline" onClick={addDocument}>
                <Plus className="mr-2 h-4 w-4" />
                Add Document
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {requiredDocuments.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <FileText className="mx-auto h-12 w-12 mb-4" />
                <p>No documents configured yet</p>
                <p className="text-sm">Click "Add Document" to get started</p>
              </div>
            ) : (
              <div className="space-y-4">
                {requiredDocuments.map((doc, index) => (
                  <Card key={doc.id} className="p-4">
                    <div className="flex items-start justify-between mb-4">
                      <Badge variant="outline">Document {index + 1}</Badge>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeDocument(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                    
                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <Label>Name (Arabic)</Label>
                        <Input
                          value={doc.nameAr}
                          onChange={(e) => updateDocument(index, 'nameAr', e.target.value)}
                          placeholder="اسم المستند"
                        />
                      </div>
                      <div>
                        <Label>Name (English)</Label>
                        <Input
                          value={doc.nameEn}
                          onChange={(e) => updateDocument(index, 'nameEn', e.target.value)}
                          placeholder="Document name"
                        />
                      </div>
                    </div>
                    
                    <div className="grid gap-4 md:grid-cols-3 mt-4">
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={doc.isRequired}
                          onCheckedChange={(checked) => updateDocument(index, 'isRequired', checked)}
                        />
                        <Label>Required</Label>
                      </div>
                      <div>
                        <Label>Max Size (KB)</Label>
                        <Input
                          type="number"
                          value={doc.maxSizeKB}
                          onChange={(e) => updateDocument(index, 'maxSizeKB', Number(e.target.value))}
                        />
                      </div>
                      <div>
                        <Label>Accepted Formats</Label>
                        <Input
                          value={doc.acceptedFormats.join(', ')}
                          onChange={(e) => updateDocument(index, 'acceptedFormats', e.target.value.split(', '))}
                          placeholder="pdf, jpg, png"
                        />
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex items-center justify-end space-x-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? 'Saving...' : initialData ? 'Update' : 'Create'} Assistance Type
          </Button>
        </div>
      </form>
    </Form>
  )
}
