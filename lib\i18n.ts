
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

const resources = {
  ar: {
    translation: {
      // Navigation
      'dashboard': 'لوحة التحكم',
      'profile': 'الملف الشخصي',
      'requests': 'الطلبات',
      'tasks': 'المهام',
      'reports': 'التقارير',
      'settings': 'الإعدادات',
      'logout': 'تسجيل الخروج',
      
      // Authentication
      'login': 'تسجيل الدخول',
      'register': 'إنشاء حساب',
      'email': 'البريد الإلكتروني',
      'password': 'كلمة المرور',
      'full_name': 'الاسم الكامل',
      'national_id': 'رقم الهوية الوطنية',
      'phone_number': 'رقم الهاتف',
      'login_tawtheeq': 'تسجيل الدخول عبر توثيق',
      'login_success': 'تم تسجيل الدخول بنجاح',
      'login_error': 'خطأ في البيانات المدخلة',

      // Dashboard Enhancements
      'welcome': 'مرحباً',
      'overview_status': 'نظرة عامة على حالة',
      'system_overview': 'النظام',
      'your_requests': 'طلباتك',
      'total_beneficiaries': 'إجمالي المستفيدين',
      'total_zakat_distributed': 'إجمالي الزكاة الموزعة',
      'pending_applications': 'الطلبات المعلقة',
      'active_distributions': 'التوزيعات النشطة',
      'quick_actions': 'الإجراءات السريعة',
      'register_new_beneficiary': 'تسجيل مستفيد جديد',
      'search_beneficiaries': 'البحث عن المستفيدين',
      'generate_report': 'إنشاء تقرير',
      'manage_distributions': 'إدارة التوزيعات',
      'recent_activity': 'النشاط الأخير',
      'distribution_overview': 'نظرة عامة على التوزيع',
      'monthly_distributions': 'التوزيعات الشهرية',
      'beneficiary_categories': 'فئات المستفيدين',
      'view_all': 'عرض الكل',
      'this_month': 'هذا الشهر',
      'last_month': 'الشهر الماضي',
      'last_week': 'الأسبوع الماضي',
      'this_week': 'هذا الأسبوع',
      'increase': 'زيادة',
      'decrease': 'انخفاض',
      'from': 'من',
      'sar': 'ريال سعودي',
      'beneficiaries': 'مستفيدين',
      'applications': 'طلبات',
      'distributions': 'توزيعات',
      'completed': 'مكتمل',
      'pending': 'معلق',
      'warning': 'تحذير',
      
      // Profile
      'personal_profile': 'الملف الشخصي',
      'basic_info': 'المعلومات الأساسية',
      'family_info': 'بيانات الأسرة',
      'employment_info': 'بيانات العمل',
      'financial_info': 'المعلومات المالية',
      'marital_status': 'الحالة الاجتماعية',
      'single': 'أعزب',
      'married': 'متزوج',
      'divorced': 'مطلق',
      'widowed': 'أرمل',
      
      // Assistance Requests
      'assistance_request': 'طلب المساعدة',
      'new_request': 'طلب جديد',
      'request_type': 'نوع المساعدة',
      'requested_amount': 'المبلغ المطلوب',
      'approved_amount': 'المبلغ المعتمد',
      'description': 'الوصف',
      'attach_documents': 'إرفاق المستندات',
      'submit_request': 'إرسال الطلب',
      'requests_subtitle_applicant': 'إدارة طلبات المساعدة الخاصة بك',
      'requests_subtitle_reviewer': 'مراجعة جميع طلبات المساعدة',
      'search_requests_placeholder': 'البحث في الطلبات...',
      'request_number': 'رقم الطلب',
      'submission_date': 'تاريخ التقديم',
      'processing_stages': 'مراحل المعالجة',
      'no_requests': 'لا توجد طلبات',
      'no_requests_search': 'لم يتم العثور على طلبات تطابق البحث',
      'no_requests_applicant': 'لم تقم بتقديم أي طلبات بعد',
      'no_requests_reviewer': 'لا توجد طلبات للمراجعة',
      'submit_new_request': 'تقديم طلب جديد',
      
      // Status
      'draft': 'مسودة',
      'submitted': 'مرسل',
      
      // Workflow Stages
      'reception_review': 'مراجعة الاستقبال',
      'researcher_review': 'مراجعة الباحث',
      'banking_expert_review': 'مراجعة الخبير المصرفي',
      'department_head_review': 'مراجعة رئيس القسم',
      'admin_manager_review': 'مراجعة مدير الإدارة',
      'minister_review': 'مراجعة الوزير',
      
      // Actions
      'approve': 'موافقة',
      'reject': 'رفض',
      'return': 'إرجاع',
      'save': 'حفظ',
      'edit': 'تعديل',
      'delete': 'حذف',
      'view': 'عرض',
      'download': 'تحميل',
      'upload': 'رفع',
      
      // Common
      'loading': 'جاري التحميل...',
      'search': 'بحث',
      'filter': 'تصفية',
      'date': 'التاريخ',
      'amount': 'المبلغ',
      'status': 'الحالة',
      'notes': 'الملاحظات',
      'documents': 'المستندات',
      'history': 'التاريخ',
      
      // Dashboard Stats
      'total_requests': 'إجمالي الطلبات',
      'pending_review': 'في انتظار المراجعة',
      'approved_today': 'موافق عليها اليوم',
      'rejected_today': 'مرفوضة اليوم',
      'average_processing_days': 'متوسط أيام المعالجة',
      'total_users': 'إجمالي المستخدمين',
      
      // User Roles
      'zakat_applicant': 'مقدم طلب الزكاة',
      'reception_staff': 'موظف الاستقبال',
      'researcher': 'الباحث',
      'banking_expert': 'الخبير المصرفي',
      'department_head': 'رئيس القسم',
      'admin_manager': 'مدير الإدارة',
      'minister': 'الوزير',
      'system_admin': 'مسؤول النظام',

      // Admin Navigation
      'User Management': 'إدارة المستخدمين',
      'Assistance Types': 'أنواع المساعدات',
      'System Settings': 'إعدادات النظام',

      // Admin System
      'admin_dashboard': 'لوحة تحكم الإدارة',
      'system_administration': 'إدارة النظام',
      'admin_access_required': 'يتطلب صلاحية المدير',
      'authentication_required': 'يتطلب تسجيل الدخول',
      'please_login': 'يرجى تسجيل الدخول',
      'back_to_dashboard': 'العودة إلى لوحة التحكم',

      // Admin Navigation
      'admin_dashboard_nav': 'لوحة تحكم الإدارة',
      'user_management': 'إدارة المستخدمين',
      'assistance_types': 'أنواع المساعدات',
      'workflow_management': 'إدارة سير العمل',
      'document_configuration': 'إعدادات المستندات',
      'distribution_rules': 'قواعد التوزيع',
      'system_settings': 'إعدادات النظام',
      'audit_trail': 'سجل المراجعة',
      'data_management': 'إدارة البيانات',

      // Admin Descriptions
      'system_administration_overview': 'نظرة عامة على إدارة النظام',
      'manage_user_accounts_roles': 'إدارة حسابات المستخدمين والأدوار',
      'configure_aid_types_eligibility': 'تكوين أنواع المساعدات ومعايير الأهلية',
      'configure_approval_workflows': 'تكوين سير عمل الموافقات',
      'manage_document_requirements': 'إدارة متطلبات المستندات',
      'configure_application_distribution': 'تكوين توزيع الطلبات',
      'general_system_configuration': 'إعدادات النظام العامة',
      'view_system_activity_logs': 'عرض سجلات نشاط النظام',
      'import_export_backup': 'استيراد/تصدير ونسخ احتياطي',

      // Beneficiary Management
      'beneficiary_management': 'إدارة المستفيدين',
      'beneficiary_management_desc': 'إدارة وتتبع المستفيدين من الزكاة والمساعدات',
      'beneficiary_registration': 'تسجيل مستفيد جديد',
      'beneficiary_registration_desc': 'إضافة مستفيد جديد إلى نظام إدارة الزكاة',
      'beneficiary_list': 'قائمة المستفيدين',
      'beneficiary_profile': 'الملف الشخصي للمستفيد',
      'beneficiary_details': 'تفاصيل المستفيد',
      'beneficiary_not_found': 'المستفيد غير موجود',
      'beneficiary_not_found_desc': 'لم يتم العثور على المستفيد المطلوب',
      'back_to_beneficiaries': 'العودة إلى قائمة المستفيدين',

      // Beneficiary Stats
      'approved_beneficiaries': 'المعتمدين',
      'under_review_beneficiaries': 'قيد المراجعة',
      'total_distributions': 'إجمالي التوزيعات',
      'average_distribution': 'متوسط التوزيع',
      'pending_verification_count': 'في انتظار التحقق',
      'needs_review': 'يحتاج إلى مراجعة',
      'of_total': 'من الإجمالي',
      'per_beneficiary': 'للمستفيد',

      // Search and Filters
      'search_and_filter': 'البحث والتصفية',
      'search_placeholder': 'البحث بالاسم، رقم الهوية، أو رقم الهاتف...',
      'filter_by_status': 'تصفية بالحالة',
      'filter_by_category': 'تصفية بالفئة',
      'all_statuses': 'جميع الحالات',
      'all_categories': 'جميع الفئات',
      'no_results_found': 'لا توجد نتائج مطابقة لمعايير البحث',
      'showing_results': 'عرض {{count}} من أصل {{total}} مستفيد',
      'export': 'تصدير',

      // Beneficiary Status
      'suspended': 'معلق',
      'inactive': 'غير نشط',

      // Zakat Categories
      'fuqara': 'الفقراء',
      'masakin': 'المساكين',
      'amilin': 'العاملين عليها',
      'muallafah': 'المؤلفة قلوبهم',
      'riqab': 'في الرقاب',
      'gharimin': 'الغارمين',
      'fisabilillah': 'في سبيل الله',
      'ibnus_sabil': 'ابن السبيل',
      'primary_category': 'الفئة الأساسية',

      // Table Headers
      'name': 'الاسم',
      'total_received': 'إجمالي المستلم',

      // Actions
      'view_profile': 'عرض الملف الشخصي',
      'manage_case': 'إدارة الحالة',
      'generate_voucher': 'إنشاء قسيمة',
      'back': 'العودة',

      // Profile Tabs
      'overview': 'نظرة عامة',
      'personal_details': 'البيانات الشخصية',
      'eligibility_verification': 'الأهلية والتحقق',
      'case_management': 'إدارة الحالة',
      'distribution_history': 'سجل التوزيعات',
      'family': 'الأسرة',

      // Profile Details
      'eligibility_score': 'نقاط الأهلية',
      'high_score': 'درجة عالية',
      'family_size': 'حجم الأسرة',
      'dependents': 'معالين',
      'last_distribution': 'آخر توزيع',
      'no_distribution': 'لا يوجد',
      'not_distributed_yet': 'لم يتم التوزيع بعد',

      // Contact Information
      'contact_info': 'معلومات الاتصال',
      'account_status': 'حالة الحساب',
      'current_status': 'الحالة الحالية',
      'registration_date': 'تاريخ التسجيل',
      'next_review': 'المراجعة القادمة',

      // Personal Information
      'personal_information': 'البيانات الشخصية',
      'basic_information': 'المعلومات الأساسية',
      'name_arabic': 'الاسم بالعربية',
      'name_english': 'الاسم بالإنجليزية',
      'date_of_birth': 'تاريخ الميلاد',
      'gender': 'الجنس',
      'male': 'ذكر',
      'female': 'أنثى',

      // Family Members
      'family_members': 'أفراد الأسرة',
      'no_family_info': 'لا توجد معلومات عن أفراد الأسرة',
      'dependent': 'معال',
      'special_needs': 'احتياجات خاصة',
      'relationship': 'القرابة',
      'age': 'العمر',
      'years': 'سنة',
      'son': 'ابن',
      'daughter': 'ابنة',
      'mother': 'أم',
      'father': 'أب',

      // Documents
      'no_documents': 'لا توجد مستندات مرفوعة',
      'verified': 'محقق',
      'pending_verification': 'في انتظار التحقق',
      'upload_date': 'تاريخ الرفع',

      // Coming Soon
      'coming_soon': 'قريباً',
      'under_development': 'قيد التطوير',
      'registration_form_coming': 'نموذج التسجيل قيد التطوير',
      'registration_form_desc': 'سيتم إضافة نموذج تسجيل المستفيدين الجدد قريباً',
      'will_include_features': 'سيتضمن النموذج الميزات التالية:',
      'multi_step_form': 'نموذج متعدد الخطوات مع مؤشر التقدم',
      'dual_language_input': 'إدخال البيانات الشخصية بالعربية والإنجليزية',
      'zakat_categories_selection': 'اختيار فئات الزكاة الثمانية',
      'document_upload': 'رفع المستندات المطلوبة',
      'data_validation': 'التحقق من صحة البيانات',
      'duplicate_detection': 'كشف التكرار التلقائي',

      // System Information
      'system_name': 'نظام إدارة الزكاة',
      'system_description': 'نظام شامل لإدارة طلبات الزكاة والمساعدات',

      // Dashboard
      'role': 'الدور',
      'dashboard_subtitle': 'نظرة عامة شاملة على النظام',

      // Quick Actions Descriptions
      'Add a new beneficiary to the system': 'إضافة مستفيد جديد إلى النظام',
      'Find and manage existing beneficiaries': 'البحث عن المستفيدين الحاليين وإدارتهم',
      'Create distribution and analytics reports': 'إنشاء تقارير التوزيع والتحليلات',
      'Configure distribution categories and amounts': 'تكوين فئات ومبالغ التوزيع',
      'Manage system users and permissions': 'إدارة مستخدمي النظام والصلاحيات',
      'Configure system preferences': 'تكوين تفضيلات النظام',

      // Recent Activity
      'New beneficiary registered': 'تم تسجيل مستفيد جديد',
      'Application approved': 'تم الموافقة على الطلب',
      'Zakat distributed': 'تم توزيع الزكاة',
      'Pending review': 'في انتظار المراجعة',
      'Ahmed Mohammed Al-Rashid has been registered': 'تم تسجيل أحمد محمد الراشد',
      'Fatima Al-Zahra application approved for Zakat distribution': 'تم الموافقة على طلب فاطمة الزهراء لتوزيع الزكاة',
      '5,000 SAR distributed to 10 beneficiaries': 'تم توزيع 5,000 ريال سعودي على 10 مستفيدين',
      '3 applications require case manager review': '3 طلبات تتطلب مراجعة مدير الحالة',
      'Reception Staff': 'موظف الاستقبال',
      'Case Manager': 'مدير الحالة',
      'Finance Manager': 'مدير المالية',
      'System': 'النظام',

      // Authentication Messages
      'create_new_account': 'إنشاء حساب جديد',
      'sign_in_to_account': 'تسجيل الدخول إلى حسابك',
      'choose_login_method': 'اختر طريقة تسجيل الدخول المناسبة',
      'verifying': 'جاري التحقق...',
      'error': 'خطأ',
      'passwords_not_match': 'كلمات المرور غير متطابقة',
      'account_created_success': 'تم إنشاء الحساب بنجاح',
      'wait_admin_approval': 'يرجى انتظار موافقة الإدارة على حسابك',
      'account_creation_error': 'خطأ في إنشاء الحساب',
      'unexpected_error': 'حدث خطأ غير متوقع',
      'error_during_creation': 'حدث خطأ أثناء إنشاء الحساب',
      'invalid_credentials': 'البيانات المدخلة غير صحيحة',
      'welcome_to_system': 'مرحباً بك في نظام إدارة الزكاة',
      'error_during_login': 'حدث خطأ أثناء تسجيل الدخول',

      // Account Status
      'account_pending_approval': 'حسابك في انتظار الموافقة',
      'wait_admin_approval_desc': 'يرجى انتظار موافقة الإدارة على حسابك',

      // Dashboard
      'assigned_tasks': 'المهام المخصصة لك',
      'total_requests_desc': 'إجمالي الطلبات',
      'pending_review_desc': 'في انتظار المراجعة',
      'approved_today_desc': 'موافق عليها اليوم',
      'avg_processing_days_desc': 'متوسط أيام المعالجة',

      // Reports
      'no_reports_access': 'ليس لديك صلاحية للوصول إلى التقارير',
      'monthly_report': 'التقرير الشهري',
      'monthly_stats_desc': 'إحصائيات الطلبات والموافقات الشهرية',
      'requests_label': 'الطلبات',
      'approved_label': 'موافق',
      'rejected_label': 'مرفوض',

      // Requests
      'back_button': 'العودة',
      'request_details': 'تفاصيل الطلب',
      'download_decision': 'تحميل القرار',

      // Gender and Personal Info
      'gender_label': 'الجنس',
      'male_label': 'ذكر',
      'female_label': 'أنثى',
      'marital_status_label': 'الحالة الاجتماعية',
      'married_label': 'متزوج',
      'single_label': 'أعزب',
      'divorced_label': 'مطلق',
      'widowed_label': 'أرمل',

      // Common UI Text
      'or': 'أو',
      'no_account': 'ليس لديك حساب؟',
      'demo_accounts': 'حسابات تجريبية:',
      'applicant': 'مقدم طلب:',
      'staff_member': 'موظف:',

      // Access Control
      'access_denied': 'غير مصرح',
      'no_beneficiary_access': 'ليس لديك صلاحية للوصول إلى إدارة المستفيدين',
      'no_registration_access': 'ليس لديك صلاحية لتسجيل مستفيدين جدد',

      // Multi-Step Form
      'step': 'خطوة',
      'of': 'من',
      'complete': 'مكتمل',
      'optional': 'اختياري',
      'previous': 'السابق',
      'next': 'التالي',
      'save_draft': 'حفظ مسودة',
      'submit': 'إرسال',

      // Form Steps
      'personal_details_step': 'البيانات الشخصية',
      'contact_information_step': 'معلومات الاتصال',
      'eligibility_criteria_step': 'معايير الأهلية',
      'documentation_upload_step': 'رفع المستندات',
      'review_submit_step': 'مراجعة وإرسال',

      // Form Validation
      'field_required': 'هذا الحقل مطلوب',
      'invalid_email': 'عنوان البريد الإلكتروني غير صحيح',
      'invalid_phone': 'رقم الهاتف غير صحيح',
      'invalid_national_id': 'رقم الهوية الوطنية غير صحيح',
      'min_length': 'يجب أن يكون الحد الأدنى {{min}} أحرف',
      'max_length': 'يجب أن لا يتجاوز {{max}} حرف',
      'invalid_date': 'التاريخ غير صحيح',
      'future_date_not_allowed': 'لا يمكن أن يكون التاريخ في المستقبل',
      'invalid_arabic_name': 'يجب أن يحتوي الاسم على أحرف عربية فقط',
      'invalid_english_name': 'يجب أن يحتوي الاسم على أحرف إنجليزية فقط',
      'invalid_age': 'العمر غير صحيح',
      'invalid_postal_code': 'الرمز البريدي غير صحيح',
      'select_at_least_one_category': 'يجب اختيار فئة واحدة على الأقل',
      'min_family_size': 'حجم الأسرة يجب أن يكون 1 على الأقل',
      'max_family_size': 'حجم الأسرة لا يمكن أن يتجاوز 20',
      'min_dependents': 'عدد المعالين لا يمكن أن يكون سالباً',
      'max_dependents': 'عدد المعالين لا يمكن أن يتجاوز 19',
      'invalid_income': 'الدخل الشهري غير صحيح',
      'max_income': 'الدخل الشهري لا يمكن أن يتجاوز 50,000 ريال',
      'upload_at_least_one_document': 'يجب رفع مستند واحد على الأقل',
      'national_id_required': 'رفع صورة الهوية الوطنية مطلوب',
      'terms_must_be_accepted': 'يجب الموافقة على الشروط والأحكام',
      'data_accuracy_must_be_confirmed': 'يجب تأكيد صحة البيانات',
      'privacy_policy_must_be_accepted': 'يجب الموافقة على سياسة الخصوصية',

      // Personal Details Form
      'personal_details_description': 'أدخل المعلومات الشخصية الأساسية للمستفيد',
      'enter_name_arabic': 'أدخل الاسم الكامل بالعربية',
      'enter_name_english': 'أدخل الاسم الكامل بالإنجليزية',
      'name_arabic_description': 'الاسم كما هو مكتوب في الهوية الوطنية',
      'name_english_description': 'الاسم بالأحرف الإنجليزية',
      'enter_national_id': 'أدخل رقم الهوية الوطنية',
      'national_id_description': 'رقم الهوية الوطنية السعودية (10 أرقام)',
      'pick_date': 'اختر التاريخ',
      'date_of_birth_description': 'تاريخ الميلاد كما هو مكتوب في الهوية',
      'select_gender': 'اختر الجنس',
      'select_marital_status': 'اختر الحالة الاجتماعية',

      // Contact Information Form
      'contact_information_description': 'أدخل معلومات الاتصال والعنوان',
      'enter_phone_number': 'أدخل رقم الهاتف',
      'phone_number_description': 'رقم الهاتف المحمول (يبدأ بـ 05 أو +966)',
      'enter_email': 'أدخل البريد الإلكتروني',
      'email_description': 'البريد الإلكتروني للتواصل (اختياري)',
      'enter_address': 'أدخل العنوان التفصيلي',
      'address_description': 'العنوان الكامل بما في ذلك الحي والشارع',
      'enter_city': 'أدخل المدينة',
      'city_description': 'المدينة أو المحافظة',
      'select_region': 'اختر المنطقة',
      'region_description': 'المنطقة الإدارية في المملكة',
      'enter_postal_code': 'أدخل الرمز البريدي',
      'postal_code_description': 'الرمز البريدي (5 أرقام)',

      // Eligibility Criteria Form
      'islamic_compliance_notice': 'إشعار الامتثال الإسلامي',
      'zakat_categories_description': 'يتم تصنيف المستفيدين وفقاً للفئات الثمانية للزكاة المحددة في الشريعة الإسلامية',
      'select_applicable_categories': 'اختر الفئات المناسبة للمستفيد',
      'applicable_categories': 'الفئات المناسبة',
      'select_primary_category': 'اختر الفئة الأساسية',
      'primary_category_description': 'الفئة الأساسية التي ينتمي إليها المستفيد',
      'family_financial_info': 'المعلومات الأسرية والمالية',
      'family_financial_description': 'معلومات حول حجم الأسرة والوضع المالي',
      'family_size_description': 'العدد الإجمالي لأفراد الأسرة',
      'dependents_description': 'عدد الأشخاص المعالين',
      'monthly_income_description': 'الدخل الشهري الإجمالي بالريال السعودي',
      'has_special_needs': 'يوجد احتياجات خاصة',
      'special_needs_description': 'هل يوجد أحد من أفراد الأسرة لديه احتياجات خاصة؟',
      'special_needs_details': 'تفاصيل الاحتياجات الخاصة',
      'describe_special_needs': 'اوصف الاحتياجات الخاصة بالتفصيل',
      'special_needs_details_description': 'وصف مفصل للاحتياجات الخاصة أو الظروف الصحية',

      // Zakat Category Descriptions
      'fuqara_description': 'الفقراء - الذين لا يملكون ما يكفي لسد حاجاتهم الأساسية',
      'masakin_description': 'المساكين - الذين يملكون أقل من نصف كفايتهم',
      'amilin_description': 'العاملين عليها - الذين يعملون في جمع وتوزيع الزكاة',
      'muallafah_description': 'المؤلفة قلوبهم - الذين يُراد تأليف قلوبهم للإسلام',
      'riqab_description': 'في الرقاب - لتحرير العبيد والأسرى',
      'gharimin_description': 'الغارمين - المدينين الذين لا يستطيعون سداد ديونهم',
      'fisabilillah_description': 'في سبيل الله - للجهاد والأعمال الخيرية',
      'ibnus_sabil_description': 'ابن السبيل - المسافر المنقطع عن بلده',

      // Documentation Upload Form
      'document_requirements': 'متطلبات المستندات',
      'document_requirements_description': 'يرجى رفع المستندات المطلوبة لإتمام عملية التسجيل',
      'required_document_missing': 'مستند مطلوب مفقود',
      'national_id_document_required': 'رفع صورة الهوية الوطنية مطلوب لإتمام التسجيل',
      'upload_documents': 'رفع المستندات',
      'upload_documents_description': 'اسحب وأفلت الملفات هنا أو انقر للتصفح',
      'drag_drop_files': 'اسحب وأفلت الملفات هنا',
      'drop_files_here': 'أفلت الملفات هنا',
      'or_click_to_browse': 'أو انقر للتصفح',
      'supported_formats': 'الصيغ المدعومة',
      'uploaded_documents': 'المستندات المرفوعة',
      'uploading': 'جاري الرفع',
      'required': 'مطلوب',
      'national_id_document': 'صورة الهوية الوطنية',
      'income_certificate': 'شهادة الدخل',
      'family_card': 'كرت العائلة',
      'medical_report': 'التقرير الطبي',
      'other_document': 'مستند آخر',
      'additional_notes': 'ملاحظات إضافية',
      'additional_notes_description': 'أي معلومات إضافية تود إضافتها',
      'enter_additional_notes': 'أدخل أي ملاحظات إضافية',
      'additional_notes_help': 'معلومات إضافية قد تساعد في تقييم الطلب',

      // Review and Submit Form
      'review_information': 'مراجعة المعلومات',
      'review_information_description': 'يرجى مراجعة جميع المعلومات المدخلة قبل الإرسال',
      'not_provided': 'غير مقدم',
      'no_documents_uploaded': 'لم يتم رفع أي مستندات',
      'terms_and_conditions': 'الشروط والأحكام',
      'terms_conditions_description': 'يرجى قراءة والموافقة على الشروط والأحكام',
      'accept_terms_conditions': 'أوافق على الشروط والأحكام',
      'terms_conditions_text': 'لقد قرأت وفهمت وأوافق على شروط وأحكام نظام إدارة الزكاة',
      'confirm_data_accuracy': 'أؤكد صحة البيانات',
      'data_accuracy_text': 'أؤكد أن جميع المعلومات المقدمة صحيحة ودقيقة',
      'accept_privacy_policy': 'أوافق على سياسة الخصوصية',
      'privacy_policy_text': 'أوافق على سياسة الخصوصية وطريقة التعامل مع البيانات الشخصية',

      // Enhanced Validation Messages
      'min_value': 'يجب أن تكون القيمة {{min}} على الأقل',
      'max_value': 'يجب أن لا تتجاوز القيمة {{max}}',
      'validation_error': 'خطأ في التحقق من صحة البيانات',
      'age_below_18_warning': 'العمر أقل من 18 سنة - قد يتطلب موافقة ولي الأمر',
      'age_above_100_warning': 'العمر أكبر من 100 سنة - يرجى التحقق من التاريخ',
      'dependents_family_size_warning': 'عدد المعالين يجب أن يكون أقل من حجم الأسرة',
      'high_income_warning': 'الدخل مرتفع - قد لا يكون مؤهلاً للزكاة',
      'duplicate_beneficiary_found': 'تم العثور على مستفيد مشابه في النظام',
      'duplicate_national_id': 'رقم الهوية الوطنية مسجل مسبقاً',
      'duplicate_phone': 'رقم الهاتف مسجل مسبقاً',
      'duplicate_email': 'البريد الإلكتروني مسجل مسبقاً',
      'similar_name_found': 'تم العثور على اسم مشابه في النظام',

      // Accessibility Messages
      'form_has_error': 'النموذج يحتوي على خطأ واحد',
      'form_has_errors': 'النموذج يحتوي على {{count}} أخطاء',
      'step_announcement': 'الخطوة {{current}} من {{total}}: {{title}}',
      'progress_announcement': 'تم إكمال {{percentage}}% من النموذج',
      'required_field': 'حقل مطلوب',
      'optional_field': 'حقل اختياري',
      'error_in_field': 'خطأ في الحقل',
      'field_description': 'وصف الحقل',
      'form_navigation': 'التنقل في النموذج',
      'skip_to_content': 'تخطي إلى المحتوى',
      'skip_to_navigation': 'تخطي إلى التنقل',

      // Final Implementation Messages
      'draft_saved_successfully': 'تم حفظ المسودة بنجاح',
      'error_saving_draft': 'خطأ في حفظ المسودة',
      'duplicate_check_failed': 'فشل في فحص التكرار',
      'beneficiary_registered_successfully': 'تم تسجيل المستفيد بنجاح',
      'beneficiary_registration_success_description': 'تم إضافة المستفيد إلى النظام وسيتم مراجعة الطلب',
      'error_submitting_form': 'خطأ في إرسال النموذج',
      'draft_loaded': 'تم تحميل المسودة المحفوظة',
      'submitting_registration': 'جاري إرسال التسجيل',
      'please_wait': 'يرجى الانتظار...',
      'beneficiary_registration_description': 'قم بتسجيل مستفيد جديد في نظام إدارة الزكاة',

      // Assistance Types Management
      'assistance_types_management': 'إدارة أنواع المساعدات',
      'assistance_types_management_desc': 'تكوين أنواع المساعدات ومعايير الأهلية والمتطلبات',
      'add_new_type': 'إضافة نوع جديد',
      'create_assistance_type': 'إنشاء نوع مساعدة',
      'edit_assistance_type': 'تعديل نوع المساعدة',
      'assistance_type_details': 'تفاصيل نوع المساعدة',
      'basic_info_section': 'المعلومات الأساسية',
      'configure_basic_details': 'تكوين التفاصيل الأساسية لنوع المساعدة',
      'assistance_name_arabic': 'الاسم بالعربية',
      'assistance_name_english': 'الاسم بالإنجليزية',
      'description_arabic': 'الوصف بالعربية',
      'description_english': 'الوصف بالإنجليزية',
      'maximum_amount': 'الحد الأقصى للمبلغ',
      'maximum_amount_sar': 'الحد الأقصى للمبلغ (ريال سعودي)',
      'category': 'الفئة',
      'active_status': 'الحالة النشطة',
      'enable_assistance_type': 'تفعيل نوع المساعدة للطلبات',
      'required_documents': 'المستندات المطلوبة',
      'configure_required_documents': 'تكوين المستندات المطلوبة لنوع المساعدة',
      'add_document': 'إضافة مستند',
      'no_documents_configured': 'لم يتم تكوين أي مستندات بعد',
      'click_add_document': 'انقر على "إضافة مستند" للبدء',
      'document_name_arabic': 'اسم المستند بالعربية',
      'document_name_english': 'اسم المستند بالإنجليزية',
      'is_required': 'مطلوب',
      'max_size_kb': 'الحد الأقصى للحجم (كيلوبايت)',
      'accepted_formats': 'الصيغ المقبولة',
      'total_types': 'إجمالي الأنواع',
      'active_types': 'الأنواع النشطة',
      'inactive_types': 'الأنواع غير النشطة',
      'max_amount': 'أقصى مبلغ',
      'showing_types': 'عرض {{count}} من {{total}} أنواع المساعدات',
      'no_assistance_types': 'لا توجد أنواع مساعدات',
      'search_assistance_types': 'البحث في أنواع المساعدات...',
      'view_details': 'عرض التفاصيل',
      'activate': 'تفعيل',
      'deactivate': 'إلغاء التفعيل',
      'assistance_type_not_found': 'نوع المساعدة غير موجود',
      'assistance_type_not_found_desc': 'لم يتم العثور على نوع المساعدة المطلوب',
      'back_to_assistance_types': 'العودة إلى أنواع المساعدات',
      'create_new_assistance_type': 'إنشاء نوع مساعدة جديد',
      'configure_assistance_type': 'تكوين نوع مساعدة جديد مع معايير الأهلية والمتطلبات',
      'back_to_details': 'العودة إلى التفاصيل',
      'modify_assistance_type': 'تعديل تكوين نوع المساعدة',
      'update_assistance_type': 'تحديث نوع المساعدة',
      'assistance_type_created': 'تم إنشاء نوع المساعدة بنجاح',
      'assistance_type_updated': 'تم تحديث نوع المساعدة بنجاح',
      'failed_create_assistance_type': 'فشل في إنشاء نوع المساعدة',
      'failed_update_assistance_type': 'فشل في تحديث نوع المساعدة',
      'cancel': 'إلغاء',
      'create': 'إنشاء',
      'update': 'تحديث',
      'saving': 'جاري الحفظ...',
      'eligibility_criteria': 'معايير الأهلية',
      'usage_statistics': 'إحصائيات الاستخدام',
      'total_applications': 'إجمالي الطلبات',
      'approved': 'موافق عليها',
      'rejected': 'مرفوضة',

      // Common Admin Terms
      'manage': 'إدارة',
      'configure': 'تكوين',
      'actions': 'الإجراءات',
      'details': 'التفاصيل',
      'admin_overview': 'نظرة عامة',
      'admin_statistics': 'الإحصائيات',
      'all_operational': 'جميعها تعمل',
      'good': 'جيد',
      'all_systems_operational': 'جميع الأنظمة تعمل',
      'recently_updated': 'محدثة مؤخراً',
      'from_last_month': 'من الشهر الماضي',
      'documents_count': '{{count}} مستندات',
      'uses': 'استخدامات',
      'formats': 'الصيغ',
      'size': 'الحجم',
      'mb': 'ميجابايت',
      'status_active': 'نشط',
      'status_inactive': 'غير نشط',
      'configure_requirements': 'تكوين المتطلبات',
      'advanced_filters': 'مرشحات متقدمة',
      'most_used': 'الأكثر استخداماً',
      'total_usage': 'إجمالي الاستخدام',
    }
  },
  en: {
    translation: {
      // Navigation
      'dashboard': 'Dashboard',
      'profile': 'Profile',
      'requests': 'Requests',
      'tasks': 'Tasks',
      'reports': 'Reports',
      'settings': 'Settings',
      'logout': 'Logout',
      
      // Authentication
      'login': 'Login',
      'register': 'Register',
      'email': 'Email',
      'password': 'Password',
      'full_name': 'Full Name',
      'national_id': 'National ID',
      'phone_number': 'Phone Number',
      'login_tawtheeq': 'Login with Tawtheeq',
      'login_success': 'Login successful',
      'login_error': 'Invalid credentials',

      // Dashboard Enhancements
      'welcome': 'Welcome',
      'overview_status': 'Overview of',
      'system_overview': 'system status',
      'your_requests': 'your requests',
      'total_beneficiaries': 'Total Beneficiaries',
      'total_zakat_distributed': 'Total Zakat Distributed',
      'pending_applications': 'Pending Applications',
      'active_distributions': 'Active Distributions',
      'quick_actions': 'Quick Actions',
      'register_new_beneficiary': 'Register New Beneficiary',
      'search_beneficiaries': 'Search Beneficiaries',
      'generate_report': 'Generate Report',
      'manage_distributions': 'Manage Distributions',
      'recent_activity': 'Recent Activity',
      'distribution_overview': 'Distribution Overview',
      'monthly_distributions': 'Monthly Distributions',
      'beneficiary_categories': 'Beneficiary Categories',
      'view_all': 'View All',
      'this_month': 'This Month',
      'last_month': 'Last Month',
      'last_week': 'Last Week',
      'this_week': 'This Week',
      'increase': 'increase',
      'decrease': 'decrease',
      'from': 'from',
      'sar': 'SAR',
      'beneficiaries': 'beneficiaries',
      'applications': 'applications',
      'distributions': 'distributions',
      'completed': 'Completed',
      'pending': 'Pending',
      'warning': 'Warning',
      
      // Profile
      'personal_profile': 'Personal Profile',
      'basic_info': 'Basic Information',
      'family_info': 'Family Information',
      'employment_info': 'Employment Information',
      'financial_info': 'Financial Information',
      'marital_status': 'Marital Status',
      'single': 'Single',
      'married': 'Married',
      'divorced': 'Divorced',
      'widowed': 'Widowed',
      
      // Assistance Requests
      'assistance_request': 'Assistance Request',
      'new_request': 'New Request',
      'request_type': 'Request Type',
      'requested_amount': 'Requested Amount',
      'approved_amount': 'Approved Amount',
      'description': 'Description',
      'attach_documents': 'Attach Documents',
      'submit_request': 'Submit Request',
      'requests_subtitle_applicant': 'Manage your assistance requests',
      'requests_subtitle_reviewer': 'Review all assistance requests',
      'search_requests_placeholder': 'Search requests...',
      'request_number': 'Request Number',
      'submission_date': 'Submission Date',
      'processing_stages': 'Processing Stages',
      'no_requests': 'No requests',
      'no_requests_search': 'No requests found matching search criteria',
      'no_requests_applicant': 'You have not submitted any requests yet',
      'no_requests_reviewer': 'No requests to review',
      'submit_new_request': 'Submit New Request',
      
      // Status
      'draft': 'Draft',
      'submitted': 'Submitted',
      
      // Workflow Stages
      'reception_review': 'Reception Review',
      'researcher_review': 'Researcher Review',
      'banking_expert_review': 'Banking Expert Review',
      'department_head_review': 'Department Head Review',
      'admin_manager_review': 'Admin Manager Review',
      'minister_review': 'Minister Review',
      
      // Actions
      'approve': 'Approve',
      'reject': 'Reject',
      'return': 'Return',
      'save': 'Save',
      'edit': 'Edit',
      'delete': 'Delete',
      'view': 'View',
      'download': 'Download',
      'upload': 'Upload',
      
      // Common
      'loading': 'Loading...',
      'search': 'Search',
      'filter': 'Filter',
      'date': 'Date',
      'amount': 'Amount',
      'status': 'Status',
      'notes': 'Notes',
      'documents': 'Documents',
      'history': 'History',
      
      // Dashboard Stats
      'total_requests': 'Total Requests',
      'pending_review': 'Pending Review',
      'approved_today': 'Approved Today',
      'rejected_today': 'Rejected Today',
      'average_processing_days': 'Avg. Processing Days',
      'total_users': 'Total Users',
      
      // User Roles
      'zakat_applicant': 'Zakat Applicant',
      'reception_staff': 'Reception Staff',
      'researcher': 'Researcher',
      'banking_expert': 'Banking Expert',
      'department_head': 'Department Head',
      'admin_manager': 'Administration Manager',
      'minister': 'Minister',
      'system_admin': 'System Administrator',

      // Admin Navigation
      'User Management': 'User Management',
      'Assistance Types': 'Assistance Types',
      'System Settings': 'System Settings',

      // Admin System
      'admin_dashboard': 'Admin Dashboard',
      'system_administration': 'System Administration',
      'admin_access_required': 'Admin access required',
      'authentication_required': 'Authentication required',
      'please_login': 'Please login',
      'back_to_dashboard': 'Back to Dashboard',

      // Admin Navigation
      'admin_dashboard_nav': 'Admin Dashboard',
      'user_management': 'User Management',
      'assistance_types': 'Assistance Types',
      'workflow_management': 'Workflow Management',
      'document_configuration': 'Document Configuration',
      'distribution_rules': 'Distribution Rules',
      'system_settings': 'System Settings',
      'audit_trail': 'Audit Trail',
      'data_management': 'Data Management',

      // Admin Descriptions
      'system_administration_overview': 'System administration overview',
      'manage_user_accounts_roles': 'Manage user accounts and roles',
      'configure_aid_types_eligibility': 'Configure aid types and eligibility',
      'configure_approval_workflows': 'Configure approval workflows',
      'manage_document_requirements': 'Manage document requirements',
      'configure_application_distribution': 'Configure application distribution',
      'general_system_configuration': 'General system configuration',
      'view_system_activity_logs': 'View system activity logs',
      'import_export_backup': 'Import/export and backup',

      // Beneficiary Management
      'beneficiary_management': 'Beneficiary Management',
      'beneficiary_management_desc': 'Manage and track Zakat and assistance beneficiaries',
      'beneficiary_registration': 'New Beneficiary Registration',
      'beneficiary_registration_desc': 'Add a new beneficiary to the Zakat management system',
      'beneficiary_list': 'Beneficiary List',
      'beneficiary_profile': 'Beneficiary Profile',
      'beneficiary_details': 'Beneficiary Details',
      'beneficiary_not_found': 'Beneficiary Not Found',
      'beneficiary_not_found_desc': 'The requested beneficiary could not be found',
      'back_to_beneficiaries': 'Back to Beneficiaries List',

      // Beneficiary Stats
      'approved_beneficiaries': 'Approved',
      'under_review_beneficiaries': 'Under Review',
      'total_distributions': 'Total Distributions',
      'average_distribution': 'Average Distribution',
      'pending_verification_count': 'Pending Verification',
      'needs_review': 'Needs Review',
      'of_total': 'of Total',
      'per_beneficiary': 'per Beneficiary',

      // Search and Filters
      'search_and_filter': 'Search and Filter',
      'search_placeholder': 'Search by name, national ID, or phone number...',
      'filter_by_status': 'Filter by Status',
      'filter_by_category': 'Filter by Category',
      'all_statuses': 'All Statuses',
      'all_categories': 'All Categories',
      'no_results_found': 'No results found matching search criteria',
      'showing_results': 'Showing {{count}} of {{total}} beneficiaries',
      'export': 'Export',

      // Beneficiary Status
      'pending_verification': 'Pending Verification',
      'under_review': 'Under Review',
      'approved': 'Approved',
      'rejected': 'Rejected',
      'suspended': 'Suspended',
      'inactive': 'Inactive',

      // Zakat Categories
      'fuqara': 'The Poor',
      'masakin': 'The Needy',
      'amilin': 'Zakat Administrators',
      'muallafah': 'Those whose hearts are reconciled',
      'riqab': 'To free slaves/captives',
      'gharimin': 'Those in debt',
      'fisabilillah': 'In the cause of Allah',
      'ibnus_sabil': 'The wayfarer/traveler',
      'primary_category': 'Primary Category',

      // Table Headers
      'name': 'Name',
      'total_received': 'Total Received',

      // Actions
      'view_profile': 'View Profile',
      'manage_case': 'Manage Case',
      'generate_voucher': 'Generate Voucher',
      'back': 'Back',

      // Profile Tabs
      'overview': 'Overview',
      'personal_details': 'Personal Details',
      'eligibility_verification': 'Eligibility & Verification',
      'case_management': 'Case Management',
      'distribution_history': 'Distribution History',
      'family': 'Family',

      // Profile Details
      'eligibility_score': 'Eligibility Score',
      'high_score': 'High Score',
      'family_size': 'Family Size',
      'dependents': 'Dependents',
      'last_distribution': 'Last Distribution',
      'no_distribution': 'None',
      'not_distributed_yet': 'Not distributed yet',

      // Contact Information
      'contact_info': 'Contact Information',
      'account_status': 'Account Status',
      'current_status': 'Current Status',
      'registration_date': 'Registration Date',
      'next_review': 'Next Review',

      // Personal Information
      'personal_information': 'Personal Information',
      'basic_information': 'Basic Information',
      'name_arabic': 'Name in Arabic',
      'name_english': 'Name in English',
      'date_of_birth': 'Date of Birth',
      'gender': 'Gender',
      'male': 'Male',
      'female': 'Female',

      // Family Members
      'family_members': 'Family Members',
      'no_family_info': 'No family information available',
      'dependent': 'Dependent',
      'special_needs': 'Special Needs',
      'relationship': 'Relationship',
      'age': 'Age',
      'years': 'years',
      'son': 'Son',
      'daughter': 'Daughter',
      'mother': 'Mother',
      'father': 'Father',

      // Documents
      'no_documents': 'No documents uploaded',
      'verified': 'Verified',
      'upload_date': 'Upload Date',

      // Coming Soon
      'coming_soon': 'Coming Soon',
      'under_development': 'Under Development',
      'registration_form_coming': 'Registration Form Under Development',
      'registration_form_desc': 'New beneficiary registration form will be added soon',
      'will_include_features': 'The form will include the following features:',
      'multi_step_form': 'Multi-step form with progress indicator',
      'dual_language_input': 'Personal data input in Arabic and English',
      'zakat_categories_selection': 'Selection of eight Zakat categories',
      'document_upload': 'Required document upload',
      'data_validation': 'Data validation',
      'duplicate_detection': 'Automatic duplicate detection',

      // System Information
      'system_name': 'Zakat Management System',
      'system_description': 'Comprehensive system for managing Zakat and assistance requests',

      // Dashboard
      'role': 'Role',
      'dashboard_subtitle': 'Comprehensive system overview',

      // Quick Actions Descriptions
      'Add a new beneficiary to the system': 'Add a new beneficiary to the system',
      'Find and manage existing beneficiaries': 'Find and manage existing beneficiaries',
      'Create distribution and analytics reports': 'Create distribution and analytics reports',
      'Configure distribution categories and amounts': 'Configure distribution categories and amounts',
      'Manage system users and permissions': 'Manage system users and permissions',
      'Configure system preferences': 'Configure system preferences',

      // Recent Activity
      'New beneficiary registered': 'New beneficiary registered',
      'Application approved': 'Application approved',
      'Zakat distributed': 'Zakat distributed',
      'Pending review': 'Pending review',
      'Ahmed Mohammed Al-Rashid has been registered': 'Ahmed Mohammed Al-Rashid has been registered',
      'Fatima Al-Zahra application approved for Zakat distribution': 'Fatima Al-Zahra application approved for Zakat distribution',
      '5,000 SAR distributed to 10 beneficiaries': '5,000 SAR distributed to 10 beneficiaries',
      '3 applications require case manager review': '3 applications require case manager review',
      'Reception Staff': 'Reception Staff',
      'Case Manager': 'Case Manager',
      'Finance Manager': 'Finance Manager',
      'System': 'System',

      // Authentication Messages
      'create_new_account': 'Create new account',
      'sign_in_to_account': 'Sign in to your account',
      'choose_login_method': 'Choose the appropriate login method',
      'verifying': 'Verifying...',
      'error': 'Error',
      'passwords_not_match': 'Passwords do not match',
      'account_created_success': 'Account created successfully',
      'wait_admin_approval': 'Please wait for admin approval of your account',
      'account_creation_error': 'Error creating account',
      'unexpected_error': 'An unexpected error occurred',
      'error_during_creation': 'An error occurred during account creation',
      'invalid_credentials': 'Invalid credentials entered',
      'welcome_to_system': 'Welcome to the Zakat Management System',
      'error_during_login': 'An error occurred during login',

      // Account Status
      'account_pending_approval': 'Your account is pending approval',
      'wait_admin_approval_desc': 'Please wait for admin approval of your account',

      // Dashboard
      'assigned_tasks': 'tasks assigned to you',
      'total_requests_desc': 'Total requests',
      'pending_review_desc': 'Pending review',
      'approved_today_desc': 'Approved today',
      'avg_processing_days_desc': 'Average processing days',

      // Reports
      'no_reports_access': 'You do not have permission to access reports',
      'monthly_report': 'Monthly Report',
      'monthly_stats_desc': 'Monthly statistics of requests and approvals',
      'requests_label': 'Requests',
      'approved_label': 'Approved',
      'rejected_label': 'Rejected',

      // Requests
      'back_button': 'Back',
      'request_details': 'Request Details',
      'download_decision': 'Download Decision',

      // Gender and Personal Info
      'gender_label': 'Gender',
      'male_label': 'Male',
      'female_label': 'Female',
      'marital_status_label': 'Marital Status',
      'married_label': 'Married',
      'single_label': 'Single',
      'divorced_label': 'Divorced',
      'widowed_label': 'Widowed',

      // Common UI Text
      'or': 'or',
      'no_account': 'Don\'t have an account?',
      'demo_accounts': 'Demo Accounts:',
      'applicant': 'Applicant:',
      'staff_member': 'Staff Member:',

      // Access Control
      'access_denied': 'Access Denied',
      'no_beneficiary_access': 'You do not have permission to access beneficiary management',
      'no_registration_access': 'You do not have permission to register new beneficiaries',

      // Multi-Step Form
      'step': 'Step',
      'of': 'of',
      'complete': 'Complete',
      'optional': 'Optional',
      'previous': 'Previous',
      'next': 'Next',
      'save_draft': 'Save Draft',
      'submit': 'Submit',

      // Form Steps
      'personal_details_step': 'Personal Details',
      'contact_information_step': 'Contact Information',
      'eligibility_criteria_step': 'Eligibility Criteria',
      'documentation_upload_step': 'Documentation Upload',
      'review_submit_step': 'Review & Submit',

      // Form Validation
      'field_required': 'This field is required',
      'invalid_email': 'Invalid email address',
      'invalid_phone': 'Invalid phone number',
      'invalid_national_id': 'Invalid national ID',
      'min_length': 'Must be at least {{min}} characters',
      'max_length': 'Must not exceed {{max}} characters',
      'invalid_date': 'Invalid date',
      'future_date_not_allowed': 'Date cannot be in the future',
      'invalid_arabic_name': 'Name must contain only Arabic characters',
      'invalid_english_name': 'Name must contain only English characters',
      'invalid_age': 'Invalid age',
      'invalid_postal_code': 'Invalid postal code',
      'select_at_least_one_category': 'Must select at least one category',
      'min_family_size': 'Family size must be at least 1',
      'max_family_size': 'Family size cannot exceed 20',
      'min_dependents': 'Number of dependents cannot be negative',
      'max_dependents': 'Number of dependents cannot exceed 19',
      'invalid_income': 'Invalid monthly income',
      'max_income': 'Monthly income cannot exceed 50,000 SAR',
      'upload_at_least_one_document': 'Must upload at least one document',
      'national_id_required': 'National ID document upload is required',
      'terms_must_be_accepted': 'Terms and conditions must be accepted',
      'data_accuracy_must_be_confirmed': 'Data accuracy must be confirmed',
      'privacy_policy_must_be_accepted': 'Privacy policy must be accepted',

      // Personal Details Form
      'personal_details_description': 'Enter the basic personal information for the beneficiary',
      'enter_name_arabic': 'Enter full name in Arabic',
      'enter_name_english': 'Enter full name in English',
      'name_arabic_description': 'Name as written on the national ID',
      'name_english_description': 'Name in English characters',
      'enter_national_id': 'Enter national ID number',
      'national_id_description': 'Saudi national ID number (10 digits)',
      'pick_date': 'Pick a date',
      'date_of_birth_description': 'Date of birth as written on the ID',
      'select_gender': 'Select gender',
      'select_marital_status': 'Select marital status',

      // Contact Information Form
      'contact_information_description': 'Enter contact information and address details',
      'enter_phone_number': 'Enter phone number',
      'phone_number_description': 'Mobile phone number (starts with 05 or +966)',
      'enter_email': 'Enter email address',
      'email_description': 'Email address for communication (optional)',
      'enter_address': 'Enter detailed address',
      'address_description': 'Complete address including district and street',
      'enter_city': 'Enter city',
      'city_description': 'City or governorate',
      'select_region': 'Select region',
      'region_description': 'Administrative region in Saudi Arabia',
      'enter_postal_code': 'Enter postal code',
      'postal_code_description': 'Postal code (5 digits)',

      // Eligibility Criteria Form
      'islamic_compliance_notice': 'Islamic Compliance Notice',
      'zakat_categories_description': 'Beneficiaries are classified according to the eight Zakat categories defined in Islamic law',
      'select_applicable_categories': 'Select applicable categories for the beneficiary',
      'applicable_categories': 'Applicable Categories',
      'select_primary_category': 'Select primary category',
      'primary_category_description': 'The primary category that the beneficiary belongs to',
      'family_financial_info': 'Family & Financial Information',
      'family_financial_description': 'Information about family size and financial situation',
      'family_size_description': 'Total number of family members',
      'dependents_description': 'Number of dependent persons',
      'monthly_income_description': 'Total monthly income in Saudi Riyals',
      'has_special_needs': 'Has special needs',
      'special_needs_description': 'Does any family member have special needs?',
      'special_needs_details': 'Special needs details',
      'describe_special_needs': 'Describe the special needs in detail',
      'special_needs_details_description': 'Detailed description of special needs or health conditions',

      // Zakat Category Descriptions
      'fuqara_description': 'The Poor - Those who do not have enough to meet their basic needs',
      'masakin_description': 'The Needy - Those who have less than half of their sufficiency',
      'amilin_description': 'Zakat Administrators - Those who work in collecting and distributing Zakat',
      'muallafah_description': 'Those whose hearts are reconciled - Those whose hearts are to be won over to Islam',
      'riqab_description': 'To free slaves/captives - For freeing slaves and prisoners',
      'gharimin_description': 'Those in debt - Debtors who cannot pay their debts',
      'fisabilillah_description': 'In the cause of Allah - For jihad and charitable works',
      'ibnus_sabil_description': 'The wayfarer/traveler - Travelers stranded away from home',

      // Documentation Upload Form
      'document_requirements': 'Document Requirements',
      'document_requirements_description': 'Please upload the required documents to complete the registration process',
      'required_document_missing': 'Required Document Missing',
      'national_id_document_required': 'National ID document upload is required to complete registration',
      'upload_documents': 'Upload Documents',
      'upload_documents_description': 'Drag and drop files here or click to browse',
      'drag_drop_files': 'Drag and drop files here',
      'drop_files_here': 'Drop files here',
      'or_click_to_browse': 'Or click to browse',
      'supported_formats': 'Supported formats',
      'uploaded_documents': 'Uploaded Documents',
      'uploading': 'Uploading',
      'required': 'Required',
      'national_id_document': 'National ID Document',
      'income_certificate': 'Income Certificate',
      'family_card': 'Family Card',
      'medical_report': 'Medical Report',
      'other_document': 'Other Document',
      'additional_notes': 'Additional Notes',
      'additional_notes_description': 'Any additional information you would like to add',
      'enter_additional_notes': 'Enter any additional notes',
      'additional_notes_help': 'Additional information that may help in evaluating the request',

      // Review and Submit Form
      'review_information': 'Review Information',
      'review_information_description': 'Please review all entered information before submitting',
      'not_provided': 'Not provided',
      'no_documents_uploaded': 'No documents uploaded',
      'terms_and_conditions': 'Terms and Conditions',
      'terms_conditions_description': 'Please read and agree to the terms and conditions',
      'accept_terms_conditions': 'I accept the terms and conditions',
      'terms_conditions_text': 'I have read, understood, and agree to the terms and conditions of the Zakat Management System',
      'confirm_data_accuracy': 'I confirm data accuracy',
      'data_accuracy_text': 'I confirm that all provided information is accurate and correct',
      'accept_privacy_policy': 'I accept the privacy policy',
      'privacy_policy_text': 'I agree to the privacy policy and how personal data is handled',

      // Enhanced Validation Messages
      'min_value': 'Value must be at least {{min}}',
      'max_value': 'Value must not exceed {{max}}',
      'validation_error': 'Validation error occurred',
      'age_below_18_warning': 'Age below 18 - may require guardian approval',
      'age_above_100_warning': 'Age above 100 - please verify the date',
      'dependents_family_size_warning': 'Number of dependents should be less than family size',
      'high_income_warning': 'High income - may not be eligible for Zakat',
      'duplicate_beneficiary_found': 'Similar beneficiary found in the system',
      'duplicate_national_id': 'National ID already registered',
      'duplicate_phone': 'Phone number already registered',
      'duplicate_email': 'Email address already registered',
      'similar_name_found': 'Similar name found in the system',

      // Accessibility Messages
      'form_has_error': 'Form has one error',
      'form_has_errors': 'Form has {{count}} errors',
      'step_announcement': 'Step {{current}} of {{total}}: {{title}}',
      'progress_announcement': '{{percentage}}% of form completed',
      'required_field': 'Required field',
      'optional_field': 'Optional field',
      'error_in_field': 'Error in field',
      'field_description': 'Field description',
      'form_navigation': 'Form navigation',
      'skip_to_content': 'Skip to content',
      'skip_to_navigation': 'Skip to navigation',

      // Final Implementation Messages
      'draft_saved_successfully': 'Draft saved successfully',
      'error_saving_draft': 'Error saving draft',
      'duplicate_check_failed': 'Duplicate check failed',
      'beneficiary_registered_successfully': 'Beneficiary registered successfully',
      'beneficiary_registration_success_description': 'The beneficiary has been added to the system and the request will be reviewed',
      'error_submitting_form': 'Error submitting form',
      'draft_loaded': 'Saved draft loaded',
      'submitting_registration': 'Submitting registration',
      'please_wait': 'Please wait...',
      'beneficiary_registration_description': 'Register a new beneficiary in the Zakat Management System',

      // Assistance Types Management
      'assistance_types_management': 'Assistance Types Management',
      'assistance_types_management_desc': 'Configure aid types, eligibility criteria, and requirements',
      'add_new_type': 'Add New Type',
      'create_assistance_type': 'Create Assistance Type',
      'edit_assistance_type': 'Edit Assistance Type',
      'assistance_type_details': 'Assistance Type Details',
      'basic_info_section': 'Basic Information',
      'configure_basic_details': 'Configure the basic details of the assistance type',
      'assistance_name_arabic': 'Name (Arabic)',
      'assistance_name_english': 'Name (English)',
      'description_arabic': 'Description (Arabic)',
      'description_english': 'Description (English)',
      'maximum_amount': 'Maximum Amount',
      'maximum_amount_sar': 'Maximum Amount (SAR)',
      'category': 'Category',
      'active_status': 'Active Status',
      'enable_assistance_type': 'Enable this assistance type for applications',
      'required_documents': 'Required Documents',
      'configure_required_documents': 'Configure documents required for this assistance type',
      'add_document': 'Add Document',
      'no_documents_configured': 'No documents configured yet',
      'click_add_document': 'Click "Add Document" to get started',
      'document_name_arabic': 'Name (Arabic)',
      'document_name_english': 'Name (English)',
      'is_required': 'Required',
      'max_size_kb': 'Max Size (KB)',
      'accepted_formats': 'Accepted Formats',
      'total_types': 'Total Types',
      'active_types': 'Active Types',
      'inactive_types': 'Inactive Types',
      'max_amount': 'Max Amount',
      'showing_types': 'Showing {{count}} of {{total}} assistance types',
      'no_assistance_types': 'No assistance types found',
      'search_assistance_types': 'Search assistance types...',
      'view_details': 'View Details',
      'activate': 'Activate',
      'deactivate': 'Deactivate',
      'assistance_type_not_found': 'Assistance Type Not Found',
      'assistance_type_not_found_desc': 'The requested assistance type could not be found',
      'back_to_assistance_types': 'Back to Assistance Types',
      'create_new_assistance_type': 'Create New Assistance Type',
      'configure_assistance_type': 'Configure a new assistance type with eligibility criteria and requirements',
      'back_to_details': 'Back to Details',
      'modify_assistance_type': 'Modify the configuration of this assistance type',
      'update_assistance_type': 'Update Assistance Type',
      'assistance_type_created': 'Assistance type created successfully',
      'assistance_type_updated': 'Assistance type updated successfully',
      'failed_create_assistance_type': 'Failed to create assistance type',
      'failed_update_assistance_type': 'Failed to update assistance type',
      'cancel': 'Cancel',
      'create': 'Create',
      'update': 'Update',
      'saving': 'Saving...',
      'eligibility_criteria': 'Eligibility Criteria',
      'usage_statistics': 'Usage Statistics',
      'total_applications': 'Total Applications',
      'approved_requests': 'Approved',
      'rejected_requests': 'Rejected',

      // Common Admin Terms
      'manage': 'Manage',
      'configure': 'Configure',
      'actions': 'Actions',
      'details': 'Details',
      'admin_overview': 'Overview',
      'admin_statistics': 'Statistics',
      'all_operational': 'All operational',
      'good': 'Good',
      'all_systems_operational': 'All systems operational',
      'recently_updated': 'Recently updated',
      'from_last_month': 'From last month',
      'documents_count': '{{count}} documents',
      'uses': 'uses',
      'formats': 'Formats',
      'size': 'Size',
      'mb': 'MB',
      'status_active': 'Active',
      'status_inactive': 'Inactive',
      'configure_requirements': 'Configure Requirements',
      'advanced_filters': 'Advanced Filters',
      'most_used': 'Most Used',
      'total_usage': 'Total Usage',
    }
  }
};

// Initialize i18n immediately with resources
i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'ar',
    lng: 'ar', // Set default language
    debug: process.env.NODE_ENV === 'development',
    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
    },
    interpolation: {
      escapeValue: false,
    },
  });

export default i18n;


